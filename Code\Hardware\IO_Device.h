/*!
 * @file
 * @brief This file defines public constants, types and functions for the dc load drive module.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef __IO_DEVICE_H__
#define __IO_DEVICE_H__

#include <stdint.h>
#include <stdbool.h>
#include "Adpt_GPIO.h"

void Set_RefHeaterState(bool state);
void Set_IonGeneratorState(bool state);
uint16_t Get_IonGeneratorOnMinutes(void);
void Clear_IonGeneratorOnMinutes(void);
void Set_DefrostHeaterState(bool state);
void Set_VerticalBeamHeaterState(bool state);
void Ctrl_FrzLeftLamp(bool b_ref_left_door_state, bool b_frz_Left_door_state);
void Ctrl_FrzRightLamp(bool b_ref_right_door_state, bool b_frz_right_door_state);
bool Get_FrzLeftLampState(void);
bool Get_FrzRightLampState(void);

#endif /* __IO_DEVICE_H__ */