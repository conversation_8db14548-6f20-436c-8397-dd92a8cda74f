/*!
 * @file
 * @brief This file defines public constants, types and functions for the temperature sensor.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef DRIVER_AD_SAMPLE_H
#define DRIVER_AD_SAMPLE_H

#include <stdbool.h>
#include "Driver_AdTemperature.h"

#define U16_ZONE_TEMP_MAX ((uint16_t)1001)
#define U16_ZONE_TEMP_MIN ((uint16_t)1)

typedef enum
{
    RT_BELOW13 = 0,
    RT_BELOW18,
    RT_BELOW23,
    RT_BELOW28,
    RT_BELOW35,
    RT_BELOW40,
    RT_UP40,
    RT_MAXSIZE
} RoomTemp_em;
typedef uint8_t RoomTempRange_t;

typedef enum
{
    RT2_BELOW8 = 0,
    RT2_BELOW13,
    RT2_UP13,
    RT2_MAXSIZE
} RoomTempET_em;
typedef uint8_t RoomTempRangeET_t;


typedef enum
{
    HBELOW10,
    <PERSON><PERSON><PERSON>OW15,
    <PERSON><PERSON><PERSON><PERSON>20,
    <PERSON><PERSON><PERSON><PERSON>25,
    <PERSON><PERSON><PERSON>OW30,
    H<PERSON><PERSON>OW35,
    <PERSON><PERSON><PERSON>OW40,
    <PERSON><PERSON><PERSON>OW45,
    H<PERSON>LOW50,
    HBELOW55,
    HBE<PERSON>OW60,
    H<PERSON><PERSON>OW65,
    <PERSON><PERSON>L<PERSON>70,
    HBELOW75,
    HBELOW80,
    HBELOW85,
    HBELOW90,
    HBELOW95,
    HUP95,
    HUMIDITY_MAXSIZE
} Humidity_em;
typedef uint8_t HumidityRange_t;

typedef enum
{
    SENSOR_REF,
    SENSOR_REF_DEFROST,
    SENSOR_VV,
    SENSOR_AC,
    SENSOR_DC,
    SENSOR_HUMIDITY,
    SENSOR_ROOM,
    SENSOR_FRZ,
    SENSOR_DEFROST,
    SENSOR_TYPE_MAX
} SensorType_em;
typedef uint8_t SensorType_t;

typedef struct
{
    RoomTempRange_t roomRange;
    uint16_t u16_LowerLimit;
    uint16_t u16_UpperLimit;
} CalRoomRange_st;

typedef struct
{
    HumidityRange_t humidityRange;
    uint16_t u16_LowerLimit;
    uint16_t u16_UpperLimit;
} CalHumidityRange_st;

typedef struct
{
    uint32_t u32_SumAd;
    uint16_t u16_MaxAd;
    uint16_t u16_MinAd;
    uint16_t u16_FilteredAd;
    uint16_t u16_SensorValue;
    bool b_SensorError;
} AdSample_st;

void AdSample_Handle(void);
uint16_t Get_12VPower(void);
RoomTempRange_t Get_RoomTempRange(void);
RoomTempRangeET_t Get_RoomTempRangeET(void);
HumidityRange_t Get_HumidityRange(void);
uint16_t Get_SensorValue(SensorType_t type);
bool Get_SensorError(SensorType_t type);
bool IsRoomTempBelowEightDegree(void);
uint16_t Get_220VAdValue(void);

#endif
