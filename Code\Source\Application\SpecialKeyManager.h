/*!
 * @file
 * @brief This file defines public constants, types and functions for the special key manager.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef SPECIAL_KEY_MANAGER_H
#define SPECIAL_KEY_MANAGER_H

#include <stdint.h>
#include <stdbool.h>
#include "LedController.h"

typedef enum
{
    TMODE_NONE,
    //TMODE_T1,
    //TMODE_T2,
    //TMODE_T3,
    TMODE_FORCE_COOLING, // 强制制冷
    TMODE_FORCE_FRZCOOLING, // 强制冷冻制冷
    TMODE_FORCE_REFCOOLING, // 强制冷藏制冷
    TMODE_FORCE_DEFROST, // 强制化霜
    TMODE_FORCE_REFDEFROST, // 强制冷藏化霜
    TMODE_FORCE_ENERGY, // 强制能效
    TMODE_TT
} Tmode_em;

typedef struct
{
    void (*p_Deal_KeyActionCompleted)(void);
    uint16_t u16_UniteKeyValue; // 组合按键值
    uint16_t u16_SingleKeyValue; // 按住不动的按键值
    uint8_t u8_NeedCounter;
    bool b_IsValid;
} UniteKey_st;

#define CON_TMODE_KEY_CANCEL_TIME 3 // unit:s
#define TIME_QUERYDATEEXIT 180 // s
#define TIME_QUERYDATADISPLAYSIGN (3 * 2) // unit:0.5s
#define TIME_QUERYERROREXIT 60 // s
#define SPECIAL_SET_EXIT_TIME (10 * 2) // unit:0.5s
#define DISPLAY_SIGN_EXIT_TIME (3 * 2) // unit:0.5s
#define TIME_ADJUSTPARAMEXIT 180 // s

typedef enum
{
    CHECK_SHOWROOM_MODE = 1,

    CHECK_REF_SNR,
    CHECK_REF_VAR_SNR,
    CHECK_FRZ_SNR,

    CHECK_REF_DEF_SNR,

    CHECK_FRZ_DEF_SNR,
    CHECK_ROOM_SNR,
    CHECK_ROOM_HUMI,
    CHECK_COMP,
    CHECK_REF_DAMPER,
    CHECK_REF_VAR_DAMPER,
    CHECK_DEF_HEATER,

    CHECK_REF_FAN_DUTY,

    CHECK_FRZ_FAN_DUTY,
    CHECK_COND_FAN_DUTY,

    CHECK_VALVE_STATE,

    CHECK_REF_LEFT_DOOR_SW,
    CHECK_REF_RIGHT_DOOR_SW,
    CHECK_FRZ_LEFT_DOOR_SW,
    CHECK_FRZ_RIGHT_DOOR_SW,
    CHECK_REF_ON,
    CHECK_REF_OFF,
    CHECK_FRZ_ON,
    CHECK_FRZ_OFF,
    CHECK_VAR_ON,
    CHECK_VAR_OFF,

    CHECK_MAX
} Query_em;

extern Query_em em_QueryMode;
extern uint8_t u8_QueryData;
extern bool f_QueryError;

extern Tmode_em u8_Tmode;
extern Tmode_em u8_TmodeX;

#define DATA_NEEDINTI 0
#define DATA_AP_INQURE 1
#define DATA_DISP_SET 2

void TMode_Display(ZoneDisp_st *pst_disp);
void InquryDataDisplay(ZoneDisp_st *pst_disp, uint8_t u8_type, uint8_t u8_data);
void Ctrl_SpecialKey(uint16_t u16_keyValue);
void Adjust_SpecialMode(void);
bool Get_SpecialMode(void);
uint8_t Get_TestMode(void);
uint8_t Get_TestModeConfirm(void);
uint8_t Get_QueryMode(void);
void Exit_Tmode(void);
void Inc_AdjustPara(void);
void Dec_AdjustPara(void);
void Confirm_AdjustPara(void);

#endif
