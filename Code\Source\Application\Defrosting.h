/*!
 * @file
 * @brief This file defines public constants, types and functions for the fridge runner.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef DEFROSTING_H
#define DEFROSTING_H

#include <stdint.h>
#include <stdbool.h>
#include "SimpleFsm.h"
#include "Driver_AdSample.h"
#include "Parameter_TemperatureZone.h"

// 化霜预冷ref最长运行时间30分
#define U16_PRE_COOLING_REF_FRZ_MAX_TIME_SECONDS ((uint16_t)(30 * 60)) // 30min
#define U16_PRE_COOLING_FRZ_MAX_TIME_SECONDS_BL13_UP40 ((uint16_t)(50 * 60)) // 50min
#define U16_PRE_COOLING_FRZ_MAX_TIME_SECONDS_BL13 ((uint16_t)(50 * 60)) // 50min
#define U16_PRE_COOLING_FRZ_MAX_TIME_SECONDS_UP40 ((uint16_t)(60 * 60)) // 60min
// 化霜前空气化霜时间5分钟
#define U16_DEFROST_PRE_HOLD_MAX_TIME_SECONDS ((uint16_t)(5 * 60))
#define U8_DEFROST_PRE_HOLD_FAN_DUTY ((uint8_t)50)
// 化霜加热丝最短工作时间10秒
#define U16_DEFROST_HEATER_MIN_ON_TIME_SECONDS ((uint16_t)10)
// 化霜加热丝最长工作时间60分
#define U16_DEFROST_HEATER_MAX_ON_TIME_SECONDS ((uint16_t)(60 * 60))
// 化霜传感器故障最长化霜时间30分
#define U16_DEFROST_SENSOR_ERROR_DEFROST_HEATER_MAX_ON_TIME_SECNDS ((uint16_t)(30 * 60))
// 能耗模式化霜加热丝最长工作时间20分
#define U16_ENERGY_MODE_DEFROST_HEATER_MAX_ON_TIME_SECONDS ((uint16_t)(10 * 60))
#define U8_DEFROST_FUNCTION_ERROR_REPORT_COUNT ((uint8_t)4)
//冷藏化霜最大持续化霜时间
#define U16_REF_DEFROST_MAX_ON_TIME_SECONDS ((uint16_t)(120 * 60))
//等待冷藏化霜压机保护时间
#define U16_WAIT_REF_DEFROST_COMP_PROTECT_TIME_SECONDS ((uint16_t)(7 * 60))
#define U16_WAIT_REF_DEFROST_COMP_PROTECT_TIME_SECONDS_MIN ((uint16_t)(10))
//等待冷藏化霜冷冻风机启动延时时间
#define U16_WAIT_REF_DEFROST_FAN_DELAY_TIME_SECONDS ((uint16_t)(10 * 60))
//等待冷藏化霜压机运行时间
#define U16_WAIT_REF_DEFROST_ON_TIME_SECONDS ((uint16_t)(22 * 60))
//等待冷藏化霜检测周期
#define U16_WAIT_REF_DEFROST_CYCLE_SECONDS ((uint16_t)(15 * 60))
// 化霜退出温度
#define U16_DEFROST_EXIT_TEMPERATURE CON_8P0_DEGREE
#define U16_FORCE_DEFROST_EXIT_TEMPERATURE CON_8P0_DEGREE
#define U16_DEFROST_PRECOOL_TEMPERATURE_OFFSET 30
// 能耗模式化霜退出温度
#define U16_ENERGY_MODE_DEFROST_EXIT_TEMPERATURE CON_1P0_DEGREE
// 化霜退出温度 - 短化霜
#define U16_DEFROST_EXIT_TEMPERATURE_SHORT CON_1P0_DEGREE

// 能耗模式化霜退出温度 - 长化霜
#define U16_ENERGY_MODE_DEFROST_EXIT_TEMPERATURE_LONG CON_10P0_DEGREE
// 正常化霜退出温度 - 长化霜
#define U16_NORMAL_MODE_DEFROST_EXIT_TEMPERATURE_LONG CON_10P0_DEGREE
// 化霜退出温度 - 长化霜
#define U16_DEFROST_EXIT_TEMPERATURE_LONG CON_10P0_DEGREE

#define U16_ENERGY_MODE_DEFROST_HEATER_MAX_ON_TIME_SECONDS_RT32 ((uint16_t)(15 * 60)) // 15min
// 正常化霜退出时间 - 长化霜
#define U16_ENERGY_MODE_DEFROST_HEATER_MAX_ON_TIME_SECONDS_LONG ((uint16_t)(60 * 60)) // 60min
// 能耗模式短化霜计数
#define U8_ENERGY_MODE_DEFROST_COUNT ((uint8_t)(4))
// 正常模式短化霜计数
#define U8_NORMAL_MODE_DEFROST_COUNT ((uint8_t)(4))
// 长短化霜计数
#define U8_LONG_OR_SHORT_DEFROST_COUNT ((uint8_t)(4))
// 化霜后停机时间0分
#define U16_DEFROST_AFTER_STOP_TIME_SECONDS ((uint16_t)(0 * 60))
// 环温40以下化霜后压机保护时间7分
#define U16_DEFROST_AFTER_COMP_TIME_SECONDS ((uint16_t)(7 * 60))
// 环温40以上化霜后压机保护时间5分
#define U16_UP40_DEFROST_AFTER_COMP_TIME_SECONDS ((uint16_t)(5 * 60))
// 化霜后风机保护最短时间3分
#define U16_DEFROST_AFTER_FAN_TIME_MIN_SECONDS ((uint16_t)(3 * 60))
// 化霜后风机保护时间5分
#define U16_DEFROST_AFTER_FAN_TIME_5MIN_SECONDS ((uint16_t)(5 * 60))
// 化霜后风机保护时间8分
#define U16_DEFROST_AFTER_FAN_TIME_8MIN_SECONDS ((uint16_t)(8 * 60))
// 化霜后风机保护最长时间20分
#define U16_DEFROST_AFTER_FAN_TIME_MAX_SECONDS ((uint16_t)(20 * 60))
// 化霜后风门保护时间3分
#define U16_DEFROST_AFTER_DAMPER_TIME_SECONDS ((uint16_t)(3 * 60))
// 存储化霜类型最大个数
#define U8_DEFROST_TYPE_MAX_SAVE_NUMBER ((uint8_t)14)

enum
{
    eEnterState_First = 0, // 首次化霜
    eEnterState_OverLoadError, // 过载化霜
    eEnterState_SensorError, // 化霜传感器故障
    eEnterState_DefFunctionError, // 化霜功能故障
    eEnterState_TurboFreeze, // 速冻化霜
    eEnterState_EnergyMode, // 能耗模式化霜
    eEnterState_Normal, // 正常化霜
    eEnterState_Forbid, // 禁止化霜
    eEnterState_Max
};
typedef uint8_t EnterDefrostingState_t;

enum
{
    eTurboFreezeDefrosting_None = 0,
    eTurboFreezeDefrosting_First,
    eTurboFreezeDefrosting_Second,
    eTurboFreezeDefrosting_Max
};
typedef uint8_t TurboFreezeDefrostingState_t;

enum
{
    eDefrostMode_None = 0,
    eDefrostMode_PreWaitStart, // 等待启动压机
    eDefrostMode_PreCooling, // 预冷
    eDefrostMode_PreHold, // 空气化霜
    eDefrostMode_Defrosting, // 化霜
    eDefrostMode_WaitRefDefrost, // 等待冷藏化霜
    eDefrostMode_AfterStop, // 化霜后停压机
    eDefrostMode_AfterComp, // 化霜后压机保护
    eDefrostMode_AfterWaitStart, // 化霜后等待启压机
    eDefrostMode_AfterFan, // 化霜后风机(冷冻)保护
    eDefrostMode_AfterDamper,
    eDefrostMode_Completed,
    eDefrostMode_Max
};
typedef uint8_t DefrostMode_t;

// 定义化霜状态
typedef struct
{
    uint16_t u16_DefrostingNumber;
    uint16_t u16_PreCoolingCompStillOnTimeStart;
    uint16_t u16_PreCoolingCompStillOnTimeMinute;
    uint16_t u16_WaitRefDefCompStillOnTimeStart;
    uint16_t u16_WaitRefDefCompStillOnTimeMinute;
    uint16_t u16_DefrostHeaterOnSecond;
    uint8_t u8_DefrostFunctionErrorCount;
    bool b_DefrostFunctionError; // 化霜功能故障
    bool b_DefrostFunctionErrorReport; // 化霜功能故障
    bool refdefrostCompleted;
    bool refdefrostCompon;
    bool b_HighLoad;
    bool b_FrzFanStartup;
    bool b_FanStartupCompleted;
} Defrosting_st;

void Defrosting_Init(EnterDefrostingState_t enterState);
void Defrosting_Exit(void);
void Clear_DefrostMode(void);
DefrostMode_t Get_DefrostMode(void);
ZoneCoolingState_t Get_PreCoolingState(void);
uint16_t Get_PreCoolingtimeMinute(void);
uint16_t Get_DefrostingtimeSecond(void);
uint16_t Get_DefrostHeaterOnSecond(void);
uint16_t Get_DefrostingNumber(void);
bool Get_DefrostFunctionError(void);
bool Get_DefrostFunctionErrorReport(void);
bool Get_Deforst_First_On_Flag(void);
void Clc_Deforst_First_On_Flag(void);
uint8_t Get_EnergyModeDeforstCounter(void);
void Clear_EnergyModeDeforstCounter(void);
uint8_t Get_NormalModeDeforstCounter(void);
void Clear_NormalModeDeforstCounter(void);
uint8_t Get_LongOrShortDeforstCounter(void);
void Clear_LongOrShortDeforstCounter(void);

#endif
