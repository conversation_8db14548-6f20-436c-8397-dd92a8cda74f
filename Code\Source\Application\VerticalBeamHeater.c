/*!
 * @file
 * @brief Manages all the state variables of the cooling controller.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "VerticalBeamHeater.h"
#include "Driver_AdSample.h"
#include "Driver_GradualLamp.h"
#include "ResolverDevice.h"

static bool b_VerticalBeamHeatOutState;
static bool b_VerticalBeamHeatOutStateBackup;
static uint8_t u8_VerticalHeatSecond;
static uint8_t u8_VerticalHeatOnSecond;
static bool b_VbHeaterForcedCtrl;
static bool b_ValveSwitch;

/***********************************************************************************************************************
CONST DEFINITION
***********************************************************************************************************************/
// clang-format off
const uint8_t ARY_VertTubHeaterRate[][19] = {
    //[ 0-10%RH][10-15%RH][15-20%RH][20-25%RH][25-30%RH][30-35%RH][35-40%RH][40-45%RH]
    //[45-50%RH][50-55%RH][55-60%RH][60-65%RH][65-70%RH][70-75%RH]
    //[75-80%RH][80-85%RH][85-90%RH][90-95%RH][>95%RH]
    //0  10 15 20 25  30  35  40  45  50  55  60  65  70  75  80  85  90  95
    { 0, 0, 0, 0,  0,  0,  3,  3,  4,  4,  6,  6, 12, 12, 16, 16, 18, 18, 24 }, // RT <= 13
    { 0, 0, 0, 0,  0,  0,  4,  0,  6,  6,  6,  9, 12, 12, 18, 18, 24, 24, 30 }, // 13 < RT <= 18
    { 0, 0, 0, 0,  0,  0,  6,  4,  9,  9, 12, 12, 18, 18, 24, 24, 30, 30, 36 }, // 18 < RT <= 23
    { 0, 0, 0, 0,  0,  0, 10, 10, 16, 16, 22, 22, 28, 30, 36, 42, 48, 51, 57 }, // 23 < RT <= 28
    { 0, 0, 0, 0,  6,  6, 12, 12, 18, 18, 18, 24, 30, 36, 42, 57, 57, 57, 60 }, // 28 < RT <= 35
    { 0, 0, 0, 0, 12, 12, 24, 24, 30, 30, 36, 36, 42, 42, 48, 57, 57, 57, 60 }, // 35 < RT <= 40
    { 0, 0, 0, 0, 18, 18, 30, 30, 36, 36, 42, 42, 45, 45, 51, 57, 57, 57, 60 }, // RT > 40    
};
// clang-format on

void Ctrl_VerticalBeamHeater(void)
{
    uint8_t u8_room_range = Get_RoomTempRange();
    uint8_t u8_humidity_range = Get_HumidityRange();
    bool b_ref_lamp = Get_GradualLampState(REF_SURFACE_LAMP);

    u8_VerticalHeatOnSecond = ARY_VertTubHeaterRate[u8_room_range][u8_humidity_range];

    if((true == b_ref_lamp) || (true == b_VbHeaterForcedCtrl) || (true == b_ValveSwitch))
    {
        b_VerticalBeamHeatOutState = 0;
        u8_VerticalHeatSecond = 0;
    }
    else
    {
        u8_VerticalHeatSecond++;

        if(u8_VerticalHeatSecond >= 60)
        {
            u8_VerticalHeatSecond = 0;
        }

        if(u8_VerticalHeatSecond < u8_VerticalHeatOnSecond)
        {
            b_VerticalBeamHeatOutState = 1;
        }
        else
        {
            b_VerticalBeamHeatOutState = 0;
        }
    }
}

void Drive_VerticalBeamHeaterOutPut(void)
{
    if(b_VerticalBeamHeatOutStateBackup != b_VerticalBeamHeatOutState)
    {
        b_VerticalBeamHeatOutStateBackup = b_VerticalBeamHeatOutState;
        Vote_DeviceStatus((uint8_t)FSM_NormalControl, (uint8_t)DEVICE_VerticalBeamHeater, b_VerticalBeamHeatOutState);
    }
}

void Forced_VerticalBeamHeaterState(bool state)
{
    b_VbHeaterForcedCtrl = state;
}

void Set_ValveState(bool state)
{
    b_ValveSwitch = state;
}

bool Get_VerticalBeamHeaterState(void)
{
    return (b_VerticalBeamHeatOutState);
}

uint8_t Get_VerticalBeamHeaterOnSecond(void)
{
    return (u8_VerticalHeatOnSecond);
}
