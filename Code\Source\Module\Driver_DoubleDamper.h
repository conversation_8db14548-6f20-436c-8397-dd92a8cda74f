/*!
 * @file
 * @brief This file defines public constants, types and functions for the single damper.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef _DRIVER_DOUBLEDAMPER_H_
#define _DRIVER_DOUBLEDAMPER_H_

#include <stdint.h>
#include <stdbool.h>

#define ACTIVE_DAMPER 0U
#define SLAVE_DAMPER 1U

#define U16_DOUBLE_DAMPER_TEST_STEPS ((uint16_t)60000)

#define U16_DOUBLE_DAMPER_OPEN_STEPS ((uint16_t)6330)
#define U16_DOUBLE_DAMPER_CLOSE_STEPS ((uint16_t)6330)

// 温度保护1°C
#define U16_DOUBLE_DAMPER_TEMP_PROTECT_VALUE ((uint16_t)1 * 10)

#define U16_DOUBLE_DAMPER_PROTECT_TIME_SECOND ((uint16_t)(1 * 60))

// 温度保护时间75分钟(取消该功能)
#define U16_DOUBLE_DAMPER_TEMP_PROTECT_TIME_SECOND ((uint16_t)(75 * 60))

// 双风门强制复位时间60分钟
#define U16_DOUBLE_DAMPER_FORCED_RESET_TIME_SECOND ((uint16_t)(60 * 60))
// CW ROTATION
#define U8_DOUBLE_DAMPER_OPEN_DIRECTION ((uint8_t)1)
// CCW ROTATION
#define U8_DOUBLE_DAMPER_CLOSE_DIRECTION ((uint8_t)0)
// 7x0.5=3.5ms
#define U8_DOUBLE_DAMPER_RUN_PPS_TIME_HALF_MSEL ((uint8_t)6)

#define U8_DOUBLE_DAMPER_START_DELAY_TIME_SECOND ((uint8_t)2)

typedef enum
{
    ACTIVE_DAMPER_STATE_ALLCLOSE = 0,
    ACTIVE_DAMPER_STATE_ALLOPEN,
    ACTIVE_DAMPER_STATE_MAX
} ActiveDamperState_em;

typedef enum
{
    SLAVE_DAMPER_STATE_ALLCLOSE = 0,
    SLAVE_DAMPER_STATE_ALLOPEN,
    SLAVE_DAMPER_STATE_MAX
} SlaveDamperState_em;

typedef enum
{
    DOUBLE_DAMPER_STATE_ALLCLOSE_LIMIT = 0,
    DOUBLE_DAMPER_STATE_ALLCLOSE,
    DOUBLE_DAMPER_STATE_ACTIVEOPEN_SLAVECLOSE,
    DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT,
    DOUBLE_DAMPER_STATE_ALLOPEN,
    DOUBLE_DAMPER_STATE_ACTIVECLOSE_SLAVEOPEN,
    DOUBLE_DAMPER_STATE_MAX
} DoubleDamperState_em;

typedef enum
{
    DOUBLE_DAMPER_STEP_STATE_A,
    DOUBLE_DAMPER_STEP_STATE_B,
    DOUBLE_DAMPER_STEP_STATE_C,
    DOUBLE_DAMPER_STEP_STATE_D,
    DOUBLE_DAMPER_STEP_STATE_MAX
} DoubleDamperStepState_em;

typedef struct
{
    uint16_t u16_ActiveDamperNoActionSecond; // 主动门未动作时间
    uint16_t u16_SlaveDamperNoActionSecond; // 从动门未动作时间

    uint16_t u16_ActiveDamperZoneSnrValue; // 主动门动作开始传感器温度
    uint16_t u16_SlaveDamperZoneSnrValue; // 从动门动作开始传感器温度

    uint16_t u16_DoubleDamperRunSteps; // 双风门转动步数

    DoubleDamperState_em em_DoubleDamperNowState; // 当前位置
    DoubleDamperState_em em_DoubleDamperMidState; // 中间位置
    DoubleDamperState_em em_DoubleDamperNewState; // 新位置

    ActiveDamperState_em em_ActiveDamperNowState; // 当前位置
    ActiveDamperState_em em_ActiveDamperTargetState; // 目标位置
    ActiveDamperState_em em_ActiveDamperNewState; // 新位置

    SlaveDamperState_em em_SlaveDamperNowState; // 当前位置
    SlaveDamperState_em em_SlaveDamperTargetState; // 目标位置
    SlaveDamperState_em em_SlaveDamperNewState; // 新位置

    DoubleDamperStepState_em em_DoubleDamperStepState;

    uint8_t u8_DoubleDamperPPSTimer; // 双风门转动时间
    uint8_t u8_DoubleDamperIdleTimer; // 双风门空闲时间

    bool f_DoubleDamperInitialized;
    bool f_DoubleDamperRunning;
    bool f_DoubleDamperNeedToReset;
    bool f_DoubleDamperRunDirection;
	bool f_AlreadyRest;
    bool f_RefDoorOpen;
    bool f_FrzDoorOpen;
} DoubleDamperDriver_st;

void Init_DoubleDamper(void);
void Driver_DoubleDamper(void);
void Set_ActiveDamperState(ActiveDamperState_em em_damper_setting_state);
void Set_SlaveDamperState(SlaveDamperState_em em_damper_setting_state);
bool IsDoubleDamperAlreadyReset(void);
void Reset_DoubleDamper(void);
void ForcedCtrl_DoubleDamper(bool b_double_damper_forced_state);
void Handle_DoubleDamperTimer(void);
bool Get_ActiveDamperState(void);
bool Get_SlaveDamperState(void);
void Set_RefDoorState_DoubleDamper(bool b_door_state);
void Set_FrzDoorState_DoubleDamper(bool b_door_state);

#endif
