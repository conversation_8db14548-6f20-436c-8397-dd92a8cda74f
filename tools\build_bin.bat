set UV=C:\Keil_v5\UV4\UV4.exe
set BOOTPATH= ..\Bootloader\MDK-ARM\HC32L18x.uvprojx
set BOOTBIN=..\Bootloader\MDK-ARM\output\release\HC32L18x.bin
set BOOTLOG= out\bootbuild.log
set BOOTVERSIONFILE=..\Bootloader\Debug\syslog.h
set APPPATH= ..\Code\MDK-ARM\HC32L18x.uvprojx
set APPBIN=..\Code\MDK-ARM\output\release\HC32L18x.bin
set APPLOG= out\appbuild.log
set MODELFILE=..\Code\Source\Iot\user\user_config.h

choice /C 12 /M "[1]bs39e  [2]bf11e"
set MACHINE=%ERRORLEVEL%
if %MACHINE% == 1 (
set MODEL=bs39e
set PID=28801
set OTA_VERSION=1
set MACHINE_TYPE_FRANCE=CROSS
) ELSE if %MACHINE% == 2 (
set MODEL=bf11e
set PID=28205
set OTA_VERSION=1
set MACHINE_TYPE_FRANCE=FRANCE
) ElSE (
pause > nul
)
echo %MODEL%  %PID%


set APP_VERSION=0001
set BOOT_VERSION=0003
set BOOT_MAX_SIZE=0x5C00
set APP_MAX_SIZE=0x30E00
set APP_HEADER_OFFSET=0x5FF0
set APP_FLASH_SIZE=0x40000

set INVERTER_COMPRESSOR=VTB1114Y
set INVERTER_BOOT_VERSION=0002
set INVERTER_HWVERSION=0001
set INVERTER_APP_VERSION=0001
set INVERTER_BOOT_MAX_SIZE=0x3000
set INVERTER__HEADER_OFFSET=0x3000
set INVERTER_MAX_SIZE=0x6000



del out
del main_boot.bin
del main_app.bin
del inverter_boot.bin
del inverter_app.bin
del %BOOTBIN%
del %APPBIN%
mkdir out
copy  sed.exe out\
copy merge_ota.exe out\
copy  crc.exe out\
copy *.dll out\
copy  ota.ini out\

cd out
sed -i  "/BOOT_VERSION/c \#define BOOT_VERSION \"%BOOT_VERSION%\""  ..\%BOOTVERSIONFILE%
sed -i "/USER_MODEL/c \#define USER_MODEL \"midjd.fridge.%MODEL%\"" ..\%MODELFILE%
sed -i "/BLE_PID/c \#define BLE_PID \"%PID%\"" ..\%MODELFILE%
sed -i "/OTA_FACTORY_VERSION/c \#define OTA_FACTORY_VERSION \(%OTA_VERSION%)" ..\%MODELFILE%
sed -i "/MACHINE_TYPE_CHOOSE_FRANCE/c \#define MACHINE_TYPE_CHOOSE_FRANCE \%MACHINE_TYPE_FRANCE%" ..\%MODELFILE%

cd ..
%UV% -j0 -b %BOOTPATH% -l %BOOTLOG%
%UV% -j0 -b %APPPATH% -l %APPLOG%
copy %BOOTBIN% .\main_boot.bin
copy %APPBIN%  .\main_app.bin

copy ..\Bootloader\MDK-ARM\output\release\HC32L18x.bin  .\main_boot.bin
copy ..\Code\MDK-ARM\output\release\HC32L18x.bin  .\main_app.bin
copy inverter_bin\boot\%INVERTER_BOOT_VERSION%\boot.bin  .\inverter_boot.bin
copy inverter_bin\app\%INVERTER_APP_VERSION%\app.bin  .\inverter_app.bin
mkdir out
ImageHeader.exe %APP_VERSION% main_app.bin  %APP_MAX_SIZE%  > out/temp
setlocal enabledelayedexpansion
for /f "usebackq tokens=*" %%a in ("out/temp") do (
set "strs=%%a"
if not "!strs!" == "!strs:CRC:=!" (
set crc=!strs:CRC:=!
set APP_CRC=0x!crc!
echo !crc! 
echo !APP_CRC!
)
)

ImageHeader_inverter.exe %INVERTER_HWVERSION% %INVERTER_APP_VERSION% inverter_app.bin  %INVERTER_MAX_SIZE%  > out/temp
setlocal enabledelayedexpansion
for /f "usebackq tokens=*" %%a in ("out/temp") do (
set "strs=%%a"
if not "!strs!" == "!strs:CRC:=!" (
set crc=!strs:CRC:=!
set INVERTER_CRC=0x!crc!
echo !crc! 
echo !INVERTER_CRC!
)
)


move main_app_header.bin  out\
move inverter_app_header.bin  out\
srec_cat -output out\%MODEL%_main_flash_factory -binary main_boot.bin -binary -fill 0xff 0x0 %APP_HEADER_OFFSET% out\main_app_header.bin -binary -offset %APP_HEADER_OFFSET% -fill 0xff %APP_HEADER_OFFSET% %APP_FLASH_SIZE% 
srec_cat -output out\%MODEL%_main_flash -binary main_boot.bin -binary -fill 0xff 0x0 %APP_HEADER_OFFSET% out\main_app_header.bin -binary -offset %APP_HEADER_OFFSET%
srec_cat -output out\%MODEL%_%INVERTER_COMPRESSOR%_inverter_flash -binary inverter_boot.bin -binary -fill 0xff 0x0 %INVERTER__HEADER_OFFSET% out\inverter_app_header.bin -binary -offset %INVERTER__HEADER_OFFSET% 
ImageCrc16.exe out\%MODEL%_main_flash_factory %BOOT_VERSION% %BOOT_MAX_SIZE% %APP_VERSION%  %APP_HEADER_OFFSET%
ImageCrc16.exe out\%MODEL%_main_flash %BOOT_VERSION% %BOOT_MAX_SIZE% %APP_VERSION%  %APP_HEADER_OFFSET%
ImageCrc16_inverter.exe out\%MODEL%_%INVERTER_COMPRESSOR%_inverter_flash %INVERTER_BOOT_VERSION% %INVERTER_BOOT_MAX_SIZE% %INVERTER_APP_VERSION% %INVERTER__HEADER_OFFSET%

copy out\main_app_header.bin out\main_ota.bin 
copy out\inverter_app_header.bin out\inverter_ota.bin

cd out 
sed -i "/hw_id/c \hw_id=%MODEL%" ota.ini
sed -i "/total_version/c \total_version=%OTA_VERSION%"  ota.ini
sed -i "/main_version/c \main_version=%APP_VERSION%"  ota.ini
sed -i "/main_crc/c \main_crc=!APP_CRC!" ota.ini
sed -i "/inverter_version/c \inverter_version=%INVERTER_APP_VERSION%" ota.ini
sed -i "/inverter_crc/c \inverter_crc=!INVERTER_CRC!" ota.ini
merge_ota.exe 
crc.exe
move fw_crc.bin %MODEL%_ota_%OTA_VERSION%.bin
del  *.exe
del  *.dll
del  sed*
endlocal
pause > nul
