/*!
 * @file
 * @brief System time base.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stdbool.h>
#include "Timebase.h"
#include "Core_TimeBase.h"
#include "Driver_GradualLamp.h"
#include "Driver_CompFrequency.h"
#include "Driver_DoubleDamper.h"
#include "Drive_Valve.h"
#include "Adpt_GPIO.h"
#include "Driver_Fan.h"
#include "SystemTimerModule.h"
#include "TestUsart.h"
#include "DisplayUsart.h"
#include "InverterUsart.h"
#include "miio_api.h"

static uint8_t u8_Millisec;
static uint8_t u8_OneMsecCount;

void ISR_Timer_1ms(void)
{
    u8_Millisec++;
    Core_TimeBase_Execute_ISR();
    Add_MSecCount();
    Handle_UartTestOverTime();
    Handle_UartDisplayOverTime();
    Handle_UartInverterOverTime();
    Miio_Count_1ms();
}

void ISR_Timer_500us(void)
{
    u8_OneMsecCount++;
    Driver_DoubleDamper();

    if(u8_OneMsecCount >= 2)
    {
        u8_OneMsecCount = 0;
        Count_AllFanFbPulse();
        Driver_GradualLamp();
        Drive_ValveRun();
    }
}

uint8_t Get_SystemMillisec(void)
{
    return u8_Millisec;
}
