/*!
 * @file
 * @brief Manages all the state variables of the self-checking.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "SelfChecking.h"
#include "Core_CallBackTimer.h"
#include "ResolverDevice.h"
#include "Driver_GradualLamp.h"
#include "IO_Device.h"
#include "Driver_DoubleDamper.h"
#include "Driver_CompFrequency.h"
#include "SystemManager.h"
#include "Driver_Fan.h"
#include "DisplayInterface.h"
#include "DisplayUsart.h"

#define U16_SELF_CHECK_CYCLE_SECOND (uint16_t)1

static st_CoreCallbackTimer st_SelfCheckingTimer;
static uint8_t u8_CheckCount;

static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_SelfCheckingTimer);
}

static void SelfChecking_ControlLoad(void)
{
    if(u8_CheckCount < 1)
    {
        Reset_DoubleDamper();
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, FREQ_108HZ);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_On);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_Off);
        Test_GradualLamp(REF_SURFACE_LAMP, false);
    }
    else if(u8_CheckCount < 2)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_Off);
    }
    else if(u8_CheckCount < 3)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_On);
    }
    else if(u8_CheckCount < 4)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_Off);
    }
    else if(u8_CheckCount < 8)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 100);
    }
    else if(u8_CheckCount < 9)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_Off);
    }
    else if(u8_CheckCount < 13)
    {
        //Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, RPM_100DUTY);
    }
    else if(u8_CheckCount < 14)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_Off);
    }
    else if(u8_CheckCount < 15)
    {
        Test_GradualLamp(REF_SURFACE_LAMP, true);
    }
    else if(u8_CheckCount < 16)
    {
        Test_GradualLamp(REF_SURFACE_LAMP, false);
    }
    else if(u8_CheckCount < 17)
    {
        IO_V_TOP_LED_ENABLE;
    }
    else if(u8_CheckCount < 18)
    {
        IO_V_TOP_LED_DISABLE;
    }
    else if(u8_CheckCount < 19)
    {
        IO_FRZ_LED_ENABLE;
    }
    else if(u8_CheckCount < 20)
    {
        IO_FRZ_LED_DISABLE;
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, 0);
    }
    else
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_DontCare);
        Test_GradualLamp(REF_SURFACE_LAMP, false);
        Stop_PollTimer();
        Restart_UserInterface();
        Set_MusicType((MusicType_t)eLong_Tone);
        FridgeState_Update(eFridge_Startup);
    }
    u8_CheckCount++;
}

static void Start_PollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_SelfCheckingTimer,
        SelfChecking_ControlLoad,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

void SelfChecking_Init(void)
{
    Start_PollTimer(U16_SELF_CHECK_CYCLE_SECOND);
}

void SelfChecking_Exit(void)
{
    Stop_PollTimer();
}
