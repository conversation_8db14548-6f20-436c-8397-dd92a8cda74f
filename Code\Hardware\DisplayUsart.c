/*!
 * @file
 * @brief Initialize MCU.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Adpt_Usart.h"
#include "Core_Types.h"
#include "DisplayUsart.h"
#include "LedController.h"

static UartDisplayParm_st st_UartDisplayParm;
static uint8_t u8_KeyValue;
static MusicType_t musicType;

static void Process_UartDisplaySendData(void);
static void Process_UartDisplayReceData(const uint8_t u8_rece_data);

void Init_UartDisplay(void)
{
    st_UartDisplayParm.f_SendIE = true; // 发送允许
    st_UartDisplayParm.f_FrameSending = false;
    st_UartDisplayParm.f_FrameReceiving = false;

    st_UartDisplayParm.u8_SendTimeOutCount = 0;
    st_UartDisplayParm.u8_ReceTimeOutCount = 0;
    st_UartDisplayParm.u8_SendWaitCount = 0;
}

void Handle_UartDisplaySendData(void)
{
    if(false == st_UartDisplayParm.f_FrameSending)
    {
        st_UartDisplayParm.u8_SendTimeOutCount = 0;
    }
    else
    {
        Process_UartDisplaySendData();
    }
}

void Handle_UartDisplayReceData(const uint8_t u8_rece_data)
{
    if(true == st_UartDisplayParm.f_FrameReceiving)
    {
        if(st_UartDisplayParm.u8_ReceCount <= U8_RECE_FRAME_LENGTH_UART_DISPLAY)
        {
            Process_UartDisplayReceData(u8_rece_data);
        }
    }
}
static void Edit_UartDisplayNormalFrame(void)
{
    uint8_t u8_loop = 0;
    uint8_t u8_byte = 0;

    st_UartDisplayParm.ary_SendBuff[0] = U8_DISPLAY_FRAME_SEND_HEAD;
    st_UartDisplayParm.ary_SendBuff[1] = U8_SEND_DATA_LENGTH_UART_DISPLAY;
    u8_byte = Get_DisplayByte1Value();
    st_UartDisplayParm.ary_SendBuff[2] = u8_byte;
    u8_byte = Get_DisplayByte2Value();
    st_UartDisplayParm.ary_SendBuff[3] = u8_byte;
    u8_byte = Get_DisplayByte3Value();
    st_UartDisplayParm.ary_SendBuff[4] = u8_byte;
    u8_byte = 0;
    st_UartDisplayParm.ary_SendBuff[5] = u8_byte;
    u8_byte = 0;
    st_UartDisplayParm.ary_SendBuff[6] = u8_byte;
    u8_byte = 0;
    st_UartDisplayParm.ary_SendBuff[7] = u8_byte;
    u8_byte = 0;
    st_UartDisplayParm.ary_SendBuff[8] = u8_byte;
    u8_byte = musicType;
    st_UartDisplayParm.ary_SendBuff[9] = u8_byte;
    musicType = (MusicType_t)eNull_Tone;
    u8_byte = st_UartDisplayParm.b_ReceiveError;
    st_UartDisplayParm.ary_SendBuff[10] = u8_byte;
    u8_byte = 5;    //513这种是4，508是5，501是1
    st_UartDisplayParm.ary_SendBuff[11] = u8_byte;
    u8_byte = 0;
    st_UartDisplayParm.ary_SendBuff[12] = u8_byte;
    u8_byte = 0;
    st_UartDisplayParm.ary_SendBuff[13] = u8_byte;
    u8_byte = 0;
    st_UartDisplayParm.ary_SendBuff[14] = u8_byte;
    u8_byte = 0;
    st_UartDisplayParm.ary_SendBuff[15] = u8_byte;
    u8_byte = 0;
    st_UartDisplayParm.ary_SendBuff[16] = u8_byte;
    for(u8_loop = 0; u8_loop <= U8_SEND_DATA_LENGTH_UART_DISPLAY; u8_loop++)
    {
        st_UartDisplayParm.u8_SendCheckSum += st_UartDisplayParm.ary_SendBuff[u8_loop];
    }
    u8_byte = st_UartDisplayParm.u8_SendCheckSum;
    st_UartDisplayParm.ary_SendBuff[17] = u8_byte;
    st_UartDisplayParm.u8_SendCheckSum = 0;
}

void Handle_UartDisplayFrame(void)
{
    if((true == st_UartDisplayParm.f_SendIE) &&
        (st_UartDisplayParm.u8_SendWaitCount >= U8_DISPLAY_SEND_WAIT_MILLISECOND))
    {
        if(false == st_UartDisplayParm.f_FrameSending)
        {
            Edit_UartDisplayNormalFrame();
            st_UartDisplayParm.f_FrameSending = true;
            st_UartDisplayParm.f_FrameReceiving = false;
            st_UartDisplayParm.f_SendIE = false;

            st_UartDisplayParm.u8_SendCount = 0;
            st_UartDisplayParm.u8_SendDataState = 0;
            LpUart0_DisalbeRxInterrupts();
            LpUart0_EnalbeTxInterrupts();
        }
    }

    if(false == st_UartDisplayParm.f_FrameReceiving)
    {
        st_UartDisplayParm.u8_ReceTimeOutCount = 0;
    }
}

void Handle_UartDisplayOverTime(void)
{
    if(true == st_UartDisplayParm.f_SendIE)
    {
        st_UartDisplayParm.u8_SendWaitCount++;
    }
    else
    {
        st_UartDisplayParm.u8_SendWaitCount = 0;
    }

    if(st_UartDisplayParm.u8_SendTimeOutCount < 0xFF)
    {
        if(true == st_UartDisplayParm.f_FrameSending)
        {
            st_UartDisplayParm.u8_SendTimeOutCount++;
        }
        if(U8_DISPLAY_SEND_TIMEOUT_MILLISECOND <= st_UartDisplayParm.u8_SendTimeOutCount)
        {
            st_UartDisplayParm.u8_SendTimeOutCount = 0;
            st_UartDisplayParm.f_FrameSending = false;
            LpUart0_DisalbeTxInterrupts();
        }
    }

    if(st_UartDisplayParm.u8_ReceTimeOutCount < 0xFF)
    {
        if(true == st_UartDisplayParm.f_FrameReceiving)
        {
            st_UartDisplayParm.u8_ReceTimeOutCount++;
        }

        if(U8_DISPLAY_RECV_TIMEOUT_MILLISECOND <= st_UartDisplayParm.u8_ReceTimeOutCount)
        {
            st_UartDisplayParm.u8_ReceTimeOutCount = 0;
            st_UartDisplayParm.u8_RecvDataErrorCount++;
            if(st_UartDisplayParm.u8_RecvDataErrorCount >= U16_RECE_DATA_ERROR_TIME_WITH_100MS)
            {
                st_UartDisplayParm.b_ReceiveError = true;
            }
            st_UartDisplayParm.f_FrameReceiving = false;
            LpUart0_DisalbeRxInterrupts();
            st_UartDisplayParm.f_SendIE = true;
        }
    }
}

static void Start_UartDisplayRece(void)
{
    st_UartDisplayParm.u8_ReceDataState = 0;
    st_UartDisplayParm.u8_ReceCount = 0;
    st_UartDisplayParm.u8_ReceLength = 0;
}

static void Process_UartDisplaySendData(void)
{
    uint8_t u8_send_data = 0;

    switch(st_UartDisplayParm.u8_SendDataState)
    {
        case(uint8_t)DISPLAY_FRAME_HEAD:
            st_UartDisplayParm.u8_SendCount = 0;
            u8_send_data = st_UartDisplayParm.ary_SendBuff[st_UartDisplayParm.u8_SendCount];
            LpUart0_SendOneData(u8_send_data);
            st_UartDisplayParm.u8_SendDataState = (uint8_t)DISPLAY_FRAME_LENGTH;
            break;
        case(uint8_t)DISPLAY_FRAME_LENGTH:
            st_UartDisplayParm.u8_SendCount = 1;
            u8_send_data = st_UartDisplayParm.ary_SendBuff[st_UartDisplayParm.u8_SendCount];
            LpUart0_SendOneData(u8_send_data);
            st_UartDisplayParm.u8_SendDataState = (uint8_t)DISPLAY_FRAME_DATA;
            break;
        case(uint8_t)DISPLAY_FRAME_DATA:
            st_UartDisplayParm.u8_SendCount++;
            u8_send_data = st_UartDisplayParm.ary_SendBuff[st_UartDisplayParm.u8_SendCount];
            LpUart0_SendOneData(u8_send_data);
            if(st_UartDisplayParm.u8_SendCount >= (U8_SEND_FRAME_LENGTH_UART_DISPLAY - 1))
            {
                st_UartDisplayParm.u8_SendDataState = (uint8_t)DISPLAY_FRAME_OVER;
            }
            break;
        case(uint8_t)DISPLAY_FRAME_OVER:
            st_UartDisplayParm.f_FrameSending = false;
            st_UartDisplayParm.f_FrameReceiving = true;
            Start_UartDisplayRece();
            st_UartDisplayParm.u8_SendTimeOutCount = 0;
            LpUart0_DisalbeTxInterrupts();
            LpUart0_EnalbeRxInterrupts();
            break;
        default:
            break;
    }
}

static void Process_UartDisplayReceData(const uint8_t u8_rece_data)
{
    switch(st_UartDisplayParm.u8_ReceDataState)
    {
        case(uint8_t)DISPLAY_FRAME_HEAD:
            // 报文头
            if(U8_DISPLAY_FRAME_RECV_HEAD == u8_rece_data)
            {
                st_UartDisplayParm.ary_ReceBuff[0] = U8_DISPLAY_FRAME_RECV_HEAD;
                st_UartDisplayParm.u8_ReceCheckSum = U8_DISPLAY_FRAME_RECV_HEAD;
                st_UartDisplayParm.u8_ReceCount = 0;
                st_UartDisplayParm.u8_ReceDataState = (uint8_t)DISPLAY_FRAME_LENGTH;
            }
            break;
        case(uint8_t)DISPLAY_FRAME_LENGTH:
            // 报文长度
            if(U8_RECE_DATA_LENGTH_UART_DISPLAY == u8_rece_data)
            {
                st_UartDisplayParm.ary_ReceBuff[1] = u8_rece_data;
                st_UartDisplayParm.u8_ReceCheckSum += u8_rece_data;
                st_UartDisplayParm.u8_ReceCount = 1;
                st_UartDisplayParm.u8_ReceLength = u8_rece_data + 2;
                st_UartDisplayParm.u8_ReceDataState = (uint8_t)DISPLAY_FRAME_DATA;
            }
            break;
        case(uint8_t)DISPLAY_FRAME_DATA:
            // 正常报文数据
            st_UartDisplayParm.u8_ReceCount++;
            st_UartDisplayParm.ary_ReceBuff[st_UartDisplayParm.u8_ReceCount] = u8_rece_data;
            if(st_UartDisplayParm.u8_ReceCount <= (st_UartDisplayParm.u8_ReceLength - 2))
            {
                st_UartDisplayParm.u8_ReceCheckSum += u8_rece_data;
            }
            if(st_UartDisplayParm.u8_ReceCount >= (st_UartDisplayParm.u8_ReceLength - 1))
            {
                if(st_UartDisplayParm.u8_ReceCheckSum == st_UartDisplayParm.ary_ReceBuff[15])
                {
                    u8_KeyValue = st_UartDisplayParm.ary_ReceBuff[2];
                }
                st_UartDisplayParm.u8_ReceDataState = (uint8_t)DISPLAY_FRAME_OVER;
                st_UartDisplayParm.f_SendIE = true;
                LpUart0_DisalbeRxInterrupts();
                st_UartDisplayParm.b_ReceiveError = false;
                st_UartDisplayParm.u8_RecvDataErrorCount = 0;
                st_UartDisplayParm.u8_ReceTimeOutCount = 0;
            }
            break;
        default:
            st_UartDisplayParm.f_SendIE = true;
            LpUart0_DisalbeRxInterrupts();
            st_UartDisplayParm.b_ReceiveError = false;
            st_UartDisplayParm.u8_RecvDataErrorCount = 0;
            st_UartDisplayParm.u8_ReceTimeOutCount = 0;
            break;
    }
}

void Set_MusicType(MusicType_t type)
{
    if(type < (MusicType_t)eMax_Tone)
    {
        musicType = type;
    }
}

bool Get_DisplayCommErr(void)
{
    bool err_state = false;

    if(true == st_UartDisplayParm.b_ReceiveError)
    {
        err_state = true;
    }
    return (err_state);
}

MusicType_t Get_MusicType(void)
{
    return (musicType);
}

uint8_t Get_DisplayKeyValue(void)
{
    return (u8_KeyValue);
}
