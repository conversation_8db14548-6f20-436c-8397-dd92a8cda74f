/*!
 * @file
 * @brief This module is device IO.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "IO_Device.h"
#include "Driver_GradualLamp.h"
#include "SystemTimerModule.h"

static bool b_IonGenerator_En = false;
static     uint16_t u16_IonGenerator_TotalOnMinutes;
static     uint16_t u16_IonGenerator_StartMinute;
static bool b_FrzLeftLampState = false;
static bool b_FrzRightLampState = false;


void Set_DefrostHeaterState(bool state)
{
    if(true == state)
    {
        IO_FRZ_DEFROST_HEATER_ENABLE;
    }
    else
    {
        IO_FRZ_DEFROST_HEATER_DISABLE;
    }
}

void Set_RefHeaterState(bool state)
{
    if(true == state)
    {
        IO_AC_REF_DEFROST_HEATER_ENABLE;
    }
    else
    {
        IO_AC_REF_DEFROST_HEATER_DISABLE;
    }
}

void Set_IonGeneratorState(bool state)
{
    if(true == state)
    {
        if(b_IonGenerator_En == false)
        {
            u16_IonGenerator_StartMinute = Get_MinuteCount();
            b_IonGenerator_En = true;
        }
        IO_LZ_LED_ENABLE;
    }
    else
    {
        if(b_IonGenerator_En)
        {
            b_IonGenerator_En = false;
            u16_IonGenerator_TotalOnMinutes += Get_MinuteElapsedTime(u16_IonGenerator_StartMinute);
        }
        IO_LZ_LED_DISABLE;
    }
}

uint16_t Get_IonGeneratorOnMinutes(void)
{
    if(b_IonGenerator_En)
    {
        return u16_IonGenerator_TotalOnMinutes + Get_MinuteElapsedTime(u16_IonGenerator_StartMinute);
    }
    else
    {
        return u16_IonGenerator_TotalOnMinutes;
    }
}

void Clear_IonGeneratorOnMinutes(void)
{
    u16_IonGenerator_TotalOnMinutes = 0;
    u16_IonGenerator_StartMinute = Get_MinuteElapsedTime(u16_IonGenerator_StartMinute);
}

void Set_VerticalBeamHeaterState(bool state)
{
    bool b_ref_lamp = Get_GradualLampState(REF_SURFACE_LAMP);

    if((true == state) && (false == b_ref_lamp))
    {
        IO_CBX_LED_ENABLE;
    }
    else
    {
        IO_CBX_LED_DISABLE;
    }
}

void Ctrl_FrzRightLamp(bool b_ref_right_door_state, bool b_frz_right_door_state)
{
    bool b_state = true;

    if((false == b_frz_right_door_state) ||
        (true == b_ref_right_door_state))
    {
        b_state = false;
    }
    b_FrzRightLampState = b_state;

    if(true == b_state)
    {
        IO_V_TOP_LED_ENABLE;
    }
    else
    {
        IO_V_TOP_LED_DISABLE;
    }
}

void Ctrl_FrzLeftLamp(bool b_ref_left_door_state, bool b_frz_Left_door_state)
{
    bool b_state = true;

    if((false == b_frz_Left_door_state) ||
        (true == b_ref_left_door_state))
    {
        b_state = false;
    }
    b_FrzLeftLampState = b_state;

    if(true == b_state)
    {
        IO_FRZ_LED_ENABLE;
    }
    else
    {
        IO_FRZ_LED_DISABLE;
    }
}

bool Get_FrzLeftLampState(void)
{
    return b_FrzLeftLampState;
}

bool Get_FrzRightLampState(void)
{
    return b_FrzRightLampState;
}

