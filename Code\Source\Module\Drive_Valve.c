
/******************************************************************************
INCLIIDE 头文件包含
******************************************************************************/
#include "Core_TimeBase.h"
#include "Core_TimerLibrary.h"
#include "Core_CallBackTimer.h"
#include "SystemTimerModule.h"
#include "Drive_Valve.h"
#include "ResolverDevice.h"
#include "Driver_Emulator.h"
#include "VerticalBeamHeater.h"

/******************************************************************************
MACRO DEFINITION 宏定义
******************************************************************************/

// Number of milliseconds in 1 second (Defined just for code readability)
#define U16_NUM_MILLISECONDS_PER_SECOND ((uint16_t)(1000))
// Number of seconds in 1 minute (Defined just for code readability)
#define U16_NUM_SECONDS_PER_MINUTE ((uint16_t)(60))

#define CW      1// 顺时针方向
#define CCW 0    // 逆时针方向

#define U16_DriveValvePPSTimeMillSecond (uint16_t)30

#define U16_DriveValveIntervalTimeMillSecond (uint16_t)500

#define U32_DriveValveResetPeriodMillSecond (uint32_t)(24 * 60 * 60 * 1000)

#define U16_DriveValveStayPeriod (uint16_t)(60 * 60)

#define U16_DriveValveTestSteps ((uint16_t)65535)

#define U8_DriveValveMaxCycleSteps (uint8_t)7

/******************************************************************************
LOCAL VARIABLES 静态变量定义
******************************************************************************/

// 电动阀驱动参数
DriveValve_st st_DriveValve;


#if(VALVE_THREEWAY_200STEPS == VALVE_TYPE)

static const uint16_t ARY_Valve_RunSteps[MAX_ValveState][MAX_ValveState] =
{
    {   0,  34, 100, 154, 195},
    {  42,   0,  66, 120, 161},
    { 108,  66,   0,  54,  95},
    { 162, 120,  54,   0,  41},
    { 203, 161,  95,  41,   0}
};

#elif(VALVE_THREEWAY_48STEPS == VALVE_TYPE)

static const uint16_t ARY_Valve_RunSteps[MAX_ValveState][MAX_ValveState] =
{
    {  0,  0, 16, 32, 48},
    {  8,  0, 16, 32, 48},
    { 24, 16,  0, 16, 32},
    { 36, 32, 16,  0, 16},
    { 56, 48, 32, 16,  0}
};

#elif(VALVE_FOURWAY_48STEPS == VALVE_TYPE)

static const uint16_t ARY_Valve_RunSteps[MAX_ValveState][MAX_ValveState] =
{
    {  0,  0, 12, 24, 36, 48},
    {  8,  0, 12, 24, 36, 48},
    { 20, 12,  0, 12, 24, 36},
    { 32, 24, 12,  0, 12, 24},
    { 44, 36, 24, 12,  0, 12},
    { 56, 48, 36, 24, 12,  0},
};

#endif

uint8_t u8_unused;

// 电动阀驱动端口定义
//#define IO_Valve_EN u8_unused  //低电平有效

/******************************************************************************
GLOBAL VARIABLES 全局变量定义
******************************************************************************/


/******************************************************************************
LOCAL FUNCTIONS  静态函数声明
******************************************************************************/

// 取电动阀旋转参数
static void Drive_ValveGetRunPara(void);

// 空闲
static void Drive_ValveStepIdle(void);

// STEP0
static void Drive_ValveStep0(void);

// STEP1
static void Drive_ValveStep1(void);

// STEP2
static void Drive_ValveStep2(void);

// STEP3
static void Drive_ValveStep3(void);

// STEP4
static void Drive_ValveStep4(void);

// STEP5
static void Drive_ValveStep5(void);

// STEP6
static void Drive_ValveStep6(void);

// STEP7
static void Drive_ValveStep7(void);

/******************************************************************************
GLOBAL FUNCTIONS 全局函数声明
******************************************************************************/

// 电动阀驱动初始化
void Drive_ValveInit(void);

// 电动阀驱动强制运行或停止，做电用
void Drive_ValveForce(bool b_State);

// 电动阀复位
void Drive_ValveReset(void);

// 电动阀设置位置
void Drive_ValveSetState(ValveState_em em_ValveState);

// 电动阀旋转
void Drive_ValveRun(void);

/******************************************************************************
* function name  ：Drive_ValveInit
* description    : This function initializes variables and timers required
*                  by valve drive.
* parameters     : None
* return         ：None
* notes          ：None
******************************************************************************/

void Drive_ValveInit(void)
{
    // 阀驱动参数初始化
    //st_DriveValve.b_CtrlInit = (bool)TRUE;
    st_DriveValve.b_ForceReset = (bool)FALSE;
    st_DriveValve.b_Running = (bool)FALSE;
    st_DriveValve.b_Direction = (bool)FALSE;
    st_DriveValve.b_FirstStep = (bool)FALSE;
    st_DriveValve.b_NeedReset = (bool)FALSE;
    st_DriveValve.u32_RestTimerMsecCount = 0;
    st_DriveValve.u8_CycleSteps = 0;
    st_DriveValve.em_ValveNowState = MAX_ValveState - 1;
    st_DriveValve.em_ValveNewState = MAX_ValveState - 1;
    st_DriveValve.em_ValveTargetState = MAX_ValveState - 1;
    st_DriveValve.u16_RunSteps = 0;
    st_DriveValve.u16_PPSTimer = U16_DriveValveIntervalTimeMillSecond;
    st_DriveValve.b_AlreadyRest = false;
    st_DriveValve.b_CtrlInit = (bool)TRUE;
}

/******************************************************************************
* function name  ：Drive_ValveForce
* description    : This function force valve running or stop.
* parameters     : b_State, TRUE--running; FALSE--stop
* return         ：None
* notes          ：用于做电
******************************************************************************/

void Drive_ValveForce(bool b_State)
{
    st_DriveValve.b_ForceReset = (bool)FALSE;
    st_DriveValve.u16_PPSTimer = U16_DriveValvePPSTimeMillSecond;

    st_DriveValve.b_AlreadyRest = false;
    if ((bool)TRUE == b_State)
    {
        // 强制运行
        st_DriveValve.b_Running = (bool)TRUE;
        st_DriveValve.b_Direction = (bool)CCW;
        st_DriveValve.b_FirstStep = (bool)TRUE;
        st_DriveValve.u16_RunSteps = U16_DriveValveTestSteps;
    }
    else
    {
        // 强制停止
        st_DriveValve.b_ForceReset = (bool)FALSE;
        st_DriveValve.b_Running = (bool)FALSE;
        st_DriveValve.b_Direction = (bool)FALSE;
        st_DriveValve.b_FirstStep = (bool)FALSE;
        st_DriveValve.u16_RunSteps = MIN_UINT16;
    }
}

bool IsValveAlreadyReset(void)
{
    return st_DriveValve.b_AlreadyRest;
}

/******************************************************************************
* function name  ：Drive_ValveReset
* description    : This function reset valve.
* parameters     : None
* return         ：None
* notes          ：None
******************************************************************************/

void Drive_ValveReset(void)
{
    st_DriveValve.b_ForceReset = (bool)TRUE;
    Drive_ValveGetRunPara();
    if(st_DriveValve.b_AlreadyRest == false)
    {
        st_DriveValve.b_AlreadyRest = true;
    }
}

/******************************************************************************
* function name  ：Drive_ValveSetState
* description    : This function set valve state.
* parameters     : em_ValveTargetState
* return         ：None
* notes          ：None
******************************************************************************/

void Drive_ValveSetState(ValveState_em em_ValveTargetState)
{
    if (MAX_ValveState > em_ValveTargetState)
    {
        st_DriveValve.em_ValveTargetState = em_ValveTargetState;
    }

    if (Valve_Initial == em_ValveTargetState)
    {
        if (Valve_Initial != st_DriveValve.em_ValveNowState)
        {
            st_DriveValve.b_ForceReset = (bool)FALSE;
            st_DriveValve.em_ValveNowState = MAX_ValveState - 1;
        }
    }

    if ((bool)FALSE == st_DriveValve.b_ForceReset)
    {
        st_DriveValve.em_ValveNewState = st_DriveValve.em_ValveTargetState;
    }

    Drive_ValveGetRunPara();
}

uint8_t Get_ValveState(void)
{
    return ((uint8_t)st_DriveValve.em_ValveNowState);
}

bool Driver_ValveNeedReset(void)
{
    return (bool)st_DriveValve.b_NeedReset;
}

uint32_t Driver_ValveStateGetStayTime(ValveState_em state)
{
    if(state >= MAX_ValveState)
    {
        return 0;
    }

    return st_DriveValve.u32_StayTimerMsecCount[state] / 
    (U16_NUM_MILLISECONDS_PER_SECOND * U16_NUM_SECONDS_PER_MINUTE);
}

void Driver_ValveStateClearStayTime(void)
{
    ValveState_em state;

    for(state = Valve_Initial; state < MAX_ValveState; state++)
    {
        st_DriveValve.u32_StayTimerMsecCount[state]  = 0;
    }
}

/******************************************************************************
* function name  ：Drive_ValveGetRunPara
* description    : This function get valve run para(steps, direction).
* parameters     : None
* return         ：None
* notes          ：None
******************************************************************************/

static void Drive_ValveGetRunPara(void)
{
    if ((bool)FALSE == st_DriveValve.b_Running)
    {
        if (st_DriveValve.em_ValveNewState == st_DriveValve.em_ValveNowState)
        {
            if ((bool)TRUE == st_DriveValve.b_ForceReset)
            {
                if(st_DriveValve.em_ValveNowState != MAX_ValveState - 1)
                {
                    st_DriveValve.em_ValveNewState = MAX_ValveState - 1;
                }
                else
                {
                    st_DriveValve.em_ValveNewState = Valve_Initial;
                    st_DriveValve.b_ForceReset = (bool)FALSE;
                }
            }
            else if (st_DriveValve.em_ValveNewState !=
                     st_DriveValve.em_ValveTargetState)
            {
                st_DriveValve.em_ValveNewState =
                    st_DriveValve.em_ValveTargetState;
            }
        }

        if (st_DriveValve.em_ValveNewState != st_DriveValve.em_ValveNowState)
        {
            if ((MAX_ValveState <= st_DriveValve.em_ValveNowState) ||
                    (MAX_ValveState <= st_DriveValve.em_ValveNewState))
            {
                st_DriveValve.em_ValveNowState = MAX_ValveState - 1;
                st_DriveValve.em_ValveNewState = Valve_Initial;
            }

            if (st_DriveValve.em_ValveNewState > st_DriveValve.em_ValveNowState)
            {
                st_DriveValve.b_Direction = (bool)CW;
            }
            else
            {
                st_DriveValve.b_Direction = (bool)CCW;
            }

            st_DriveValve.b_FirstStep = (bool)TRUE;
            st_DriveValve.u16_PPSTimer = U16_DriveValvePPSTimeMillSecond;
            st_DriveValve.u16_RunSteps = ARY_Valve_RunSteps\
                                         [st_DriveValve.em_ValveNowState][st_DriveValve.em_ValveNewState];
            st_DriveValve.u32_StayTimerMsecCount[st_DriveValve.em_ValveNowState] = 0;
            st_DriveValve.em_ValveNowState = st_DriveValve.em_ValveNewState;
            st_DriveValve.b_Running = (bool)TRUE;
        }
    }
}

/******************************************************************************
* function name  ：Drive_ValveRun
* description    : This function drive the valve to run.
* parameters     : None
* return         ：None
* notes          ：None
******************************************************************************/

void Drive_ValveRun(void)
{
    if(++st_DriveValve.u32_RestTimerMsecCount > U32_DriveValveResetPeriodMillSecond)
    {
        st_DriveValve.u32_RestTimerMsecCount = 0;
        st_DriveValve.b_NeedReset = (bool)TRUE;
    }

    if(st_DriveValve.em_ValveNowState < MAX_ValveState)
    {
        st_DriveValve.u32_StayTimerMsecCount[st_DriveValve.em_ValveNowState]++;
    }
    
    if(st_DriveValve.u16_PPSTimer > 0)
    {
        st_DriveValve.u16_PPSTimer--;
    }
    else
    {
        if ((bool)FALSE == st_DriveValve.b_Running)
        {
            st_DriveValve.u16_PPSTimer = U16_DriveValveIntervalTimeMillSecond;
            Drive_ValveStepIdle();
            Set_ValveState(false);
            if (st_DriveValve.u16_RunSteps > 0)
            {
                st_DriveValve.b_Running = (bool)TRUE;
                st_DriveValve.b_FirstStep = (bool)TRUE;
            }
        }
        else
        {
            if (0 == st_DriveValve.u16_RunSteps)
            {
                Set_ValveState(false);
                if(Valve_Initial == st_DriveValve.em_ValveNowState)
                {
                    st_DriveValve.b_NeedReset = (bool)FALSE;
                    st_DriveValve.b_ForceReset = FALSE;
                    st_DriveValve.u32_RestTimerMsecCount = 0;
                }
                st_DriveValve.b_Running = (bool)FALSE;
                st_DriveValve.u16_PPSTimer = U16_DriveValveIntervalTimeMillSecond;
                Drive_ValveGetRunPara();
            }
            else if ((bool)TRUE == st_DriveValve.b_FirstStep)
            {
                Set_ValveState(true);
                st_DriveValve.b_FirstStep = (bool)FALSE;
                st_DriveValve.u16_PPSTimer = U16_DriveValveIntervalTimeMillSecond;
            }
            else
            {
                st_DriveValve.u16_PPSTimer = U16_DriveValvePPSTimeMillSecond;
                st_DriveValve.u16_RunSteps--;

                if ((bool)CW == st_DriveValve.b_Direction)
                {
                    if (U8_DriveValveMaxCycleSteps <= st_DriveValve.u8_CycleSteps)
                    {
                        st_DriveValve.u8_CycleSteps = MIN_UINT8;
                    }
                    else
                    {
                        st_DriveValve.u8_CycleSteps++;
                    }
                }
                else
                {
                    if ((MIN_UINT8 == st_DriveValve.u8_CycleSteps) ||
                            (U8_DriveValveMaxCycleSteps < st_DriveValve.u8_CycleSteps))
                    {
                        st_DriveValve.u8_CycleSteps = U8_DriveValveMaxCycleSteps;
                    }
                    else
                    {
                        st_DriveValve.u8_CycleSteps--;
                    }
                }
            }

            switch (st_DriveValve.u8_CycleSteps)
            {
                case 0:
                    Drive_ValveStep0();
                    break;

                case 1:
                    Drive_ValveStep1();
                    break;

                case 2:
                    Drive_ValveStep2();
                    break;

                case 3:
                    Drive_ValveStep3();
                    break;

                case 4:
                    Drive_ValveStep4();
                    break;

                case 5:
                    Drive_ValveStep5();
                    break;

                case 6:
                    Drive_ValveStep6();
                    break;

                case 7:
                    Drive_ValveStep7();
                    break;

                default:
                    st_DriveValve.b_Running = (bool)FALSE;
                    st_DriveValve.b_FirstStep = (bool)FALSE;
                    st_DriveValve.u8_CycleSteps = 0;
                    st_DriveValve.em_ValveNowState = MAX_ValveState - 1;
                    st_DriveValve.em_ValveNewState = Valve_Initial;
                    st_DriveValve.u16_RunSteps = 0;
                    Drive_ValveGetRunPara();
                    break;
            }
        }
    }
}

void Config_ValveIoState(bool en, bool a1_en, bool b1_en, bool a2_en, bool b2_en)
{
    if(en)
    {
        IO_Valve_EN = ON;
    }
    else
    {
        IO_Valve_EN = OFF;
    }

    if(a1_en)
    {
        IO_VALVE_A1_OFF;
    }
    else
    {
        IO_VALVE_A1_ON;
    }

    if(b1_en)
    {
        IO_VALVE_B1_OFF;
    }
    else
    {
        IO_VALVE_B1_ON;
    }

    if(a2_en)
    {
        IO_VALVE_A2_OFF;
    }
    else
    {
        IO_VALVE_A2_ON;
    }

    if(b2_en)
    {
        IO_VALVE_B2_OFF;
    }
    else
    {
        IO_VALVE_B2_ON;
    }
}

/******************************************************************************
* function name  ：Drive_ValveStepIdle
* description    : Valve drive idle.
* parameters     : None
* return         ：None
* notes          ：None
******************************************************************************/

static void Drive_ValveStepIdle(void)
{
    Config_ValveIoState(true, true, true, true, true);
}

/******************************************************************************
* function name  ：Drive_ValveStep0
* description    : Valve drive step0.
* parameters     : None
* return         ：None
* notes          ：None
******************************************************************************/

static void Drive_ValveStep0(void)
{
    Config_ValveIoState(false, false, true, true, true);
}

/******************************************************************************
* function name  ：Drive_ValveStep1
* description    : Valve drive step1.
* parameters     : None
* return         ：None
* notes          ：None
******************************************************************************/

static void Drive_ValveStep1(void)
{
    Config_ValveIoState(false, false, false, true, true);
}

/******************************************************************************
* function name  ：Drive_ValveStep2
* description    : Valve drive step2.
* parameters     : None
* return         ：None
* notes          ：None
******************************************************************************/

static void Drive_ValveStep2(void)
{
    Config_ValveIoState(false, true, false, true, true);
}

/******************************************************************************
* function name  ：Drive_ValveStep3
* description    : Valve drive step3.
* parameters     : None
* return         ：None
* notes          ：None
******************************************************************************/

static void Drive_ValveStep3(void)
{
    Config_ValveIoState(false, true, false, false, true);
}

/******************************************************************************
* function name  ：Drive_ValveStep4
* description    : Valve drive step4.
* parameters     : None
* return         ：None
* notes          ：None
******************************************************************************/

static void Drive_ValveStep4(void)
{
    Config_ValveIoState(false, true, true, false, true);
}

/******************************************************************************
* function name  ：Drive_ValveStep5
* description    : Valve drive step5.
* parameters     : None
* return         ：None
* notes          ：None
******************************************************************************/

static void Drive_ValveStep5(void)
{
    Config_ValveIoState(false, true, true, false, false);
}

/******************************************************************************
* function name  ：Drive_ValveStep6
* description    : Valve drive step6.
* parameters     : None
* return         ：None
* notes          ：None
******************************************************************************/

static void Drive_ValveStep6(void)
{
    Config_ValveIoState(false, true, true, true, false);
}

/******************************************************************************
* function name  ：Drive_ValveStep7
* description    : Valve drive step7.
* parameters     : None
* return         ：None
* notes          ：None
******************************************************************************/

static void Drive_ValveStep7(void)
{
    Config_ValveIoState(false, false, true, true, false);
}

/******************************************************************************
******************************************************************************/
