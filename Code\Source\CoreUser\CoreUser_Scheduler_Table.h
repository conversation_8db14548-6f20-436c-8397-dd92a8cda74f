/*!
 * @file
 * @brief This file contains the constant tables used for the Scheduler.
 *        This header file MUST only be included in the one file that needs access to the tables.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef __COREUSER_SCHEDULER_TABLE_H__
#define __COREUSER_SCHEDULER_TABLE_H__

#include "Core_Scheduler.h"
#include "Core_CallBackTimer.h"
#include "Driver_GradualLamp.h"
#include "Driver_DoubleDamper.h"
#include "Driver_Fan.h"
#include "Driver_Flash.h"
#include "Driver_DoorSwitch.h"
#include "Driver_AdSample.h"
#include "IO_Device.h"
#include "TestUsart.h"
#include "DisplayUsart.h"
#include "InverterUsart.h"
#include "Parameter_TemperatureZone.h"
#include "VerticalBeamHeater.h"
#include "KeyManager.h"
#include "Iot.h"
#include "Adpt_Iwdg.h"
#include "ResolverDevice.h"
#include "FaultCode.h"
#include "HeartbeatLed.h"
#include "Driver_Emulator.h"
#include "FridgeRunner.h"


/*!
 * @brief This is the scheduler table -- it contains an entry for each module's scheduler function.
 *        The entries are TSchedulerItem structure, containing an interval in milliseconds and
 *        the function to be called.
 */
static const st_CoreSchedulerItem CoreUser_Scheduler_aScheduleTable[] = {
    //{ s, ms, Pointer To Scheduled Function   }
    { 0, 0, Core_CallbackTimer_Update },
    { 0, 0, Handle_UartTestFrame },
    { 0, 0, Handle_UartDisplayFrame },
    { 0, 0, IWDG_Refesh },
    { 0, 0, Handle_UartInverterFrame },
    { 0, 0, Handle_KeyManager },
    { 0, 10, AdSample_Handle },
    { 0, 0, IOT_Func },
    { 0, 20, Update_AllDoorsSwitchState },
    { 0, 500, Update_HeartbeatLed },
    { 1, 0, Update_TempParameter },
    { 1, 0, Handle_AllDoorsState },
    { 1, 0, Process_ResolverDeviceData },
    { 1, 0, Driver_AllFan },
    { 1, 0, Handle_DoubleDamperTimer },
    { 1, 0, Drive_VerticalBeamHeaterOutPut },
    { 1, 0, Collect_FaultCode },
    { 1, 0, Update_ElectricEnergy }, // 1s 功率计算
    { 0, 100, UpdateSysParam },
    { 1, 0, Driver_Emulator_Run},
};

#endif
