/*!
 * @file
 * @brief This file defines public constants, types and functions for the frz fan resolver.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef RESOLVER_DEVICE_PRIORITY_H
#define RESOLVER_DEVICE_PRIORITY_H

#include <stdint.h>
#include <stdbool.h>
#include "Core_Types.h"
#include "ResolverDevice.h"
#include "Driver_CompFrequency.h"
#include "Driver_Fan.h"
#include "Driver_DoubleDamper.h"
#include "IO_Device.h"
#include "Drive_Valve.h"

typedef void (*TfpDeviceControlFunction)(uint8_t u8_DeviceID);

typedef struct
{
    int8_t *pai8_DeviceStatusData;
    uint8_t u8_MaxDevicePriority;
} VoteDeviceState;

// Compressor
static const uint8_t au8_CompressorPriority[] = {
    (uint8_t)FSM_SpecialModes,
    (uint8_t)FSM_NormalControl
};

// FrzFan
static const uint8_t au8_FrzFanPriority[] = {
    (uint8_t)FSM_SpecialModes,
    (uint8_t)FSM_OpenDoorControl,
    (uint8_t)FSM_NormalControl
};

// RefFan
static const uint8_t au8_RefFanPriority[] = {
    (uint8_t)FSM_SpecialModes,
    (uint8_t)FSM_OpenDoorControl,
    (uint8_t)FSM_RefFrostReduceControl,
    (uint8_t)FSM_NormalControl
};

// CoolFan
static const uint8_t au8_CoolFanPriority[] = {
    (uint8_t)FSM_SpecialModes,
    (uint8_t)FSM_OpenDoorControl,
    (uint8_t)FSM_NormalControl
};

// RefDamper
static const uint8_t au8_RefDamperPriority[] = {
    (uint8_t)FSM_SpecialModes,
    (uint8_t)FSM_OpenDoorControl,
    (uint8_t)FSM_RefFrostReduceControl,
    (uint8_t)FSM_NormalControl
};

// RefVarDamper
static const uint8_t au8_RefVarDamperPriority[] = {
    (uint8_t)FSM_SpecialModes,
    (uint8_t)FSM_OpenDoorControl,
    (uint8_t)FSM_RefFrostReduceControl,
    (uint8_t)FSM_NormalControl
};

// VerticalBeamHeaterHeater
static const uint8_t au8_VerticalBeamHeaterPriority[] = {
    (uint8_t)FSM_SpecialModes,
    (uint8_t)FSM_OpenDoorControl,
    (uint8_t)FSM_NormalControl
};

// FrzDef Heater
static const uint8_t au8_FrzDefHeaterPriority[] = {
    (uint8_t)FSM_SpecialModes,
    (uint8_t)FSM_NormalControl
};

// RefDef Heater
static const uint8_t au8_RefDefHeaterPriority[] = {
    (uint8_t)FSM_SpecialModes,
    (uint8_t)FSM_NormalControl
};

// Valve
static const uint8_t au8_ValvePriority[] = {
    (uint8_t)FSM_SpecialModes,
    (uint8_t)FSM_RefFrostReduceControl,
    (uint8_t)FSM_NormalControl
};

// IonGenerator
static const uint8_t au8_IonGeneratorPriority[] = {
    (uint8_t)FSM_SpecialModes,
    (uint8_t)FSM_OpenDoorControl,
    (uint8_t)FSM_NormalControl
};

// Create Device priority table:
static const uint8_t *const apau8_DevicePriorityTable[] = {
    au8_CompressorPriority,
    au8_FrzFanPriority,
    au8_RefFanPriority,
    au8_CoolFanPriority,
    au8_RefDamperPriority,
    au8_RefVarDamperPriority,
    au8_VerticalBeamHeaterPriority,
    au8_FrzDefHeaterPriority,
    au8_RefDefHeaterPriority,
    au8_ValvePriority,
    au8_IonGeneratorPriority,
};

static int8_t ai8_VoteStateCompressor[NUM_ELEMENTS(au8_CompressorPriority)];
static int8_t ai8_VoteStateFrzFan[NUM_ELEMENTS(au8_FrzFanPriority)];
static int8_t ai8_VoteStateRefFan[NUM_ELEMENTS(au8_RefFanPriority)];
static int8_t ai8_VoteStateCoolFan[NUM_ELEMENTS(au8_CoolFanPriority)];
static int8_t ai8_VoteStateRefDamper[NUM_ELEMENTS(au8_RefDamperPriority)];
static int8_t ai8_VoteStateRefVarDamper[NUM_ELEMENTS(au8_RefVarDamperPriority)];
static int8_t ai8_VoteStateVerticalBeamHeater[NUM_ELEMENTS(au8_VerticalBeamHeaterPriority)];
static int8_t ai8_VoteStateFrzDefHeater[NUM_ELEMENTS(au8_FrzDefHeaterPriority)];
static int8_t ai8_VoteStateRefDefHeater[NUM_ELEMENTS(au8_RefDefHeaterPriority)];
static int8_t ai8_VoteStateValve[NUM_ELEMENTS(au8_ValvePriority)];
static int8_t ai8_VoteStateIonGenerator[NUM_ELEMENTS(au8_IonGeneratorPriority)];


/******************************************************************************
* ADDING A DEVICE
* Search for the following string to find all the places you need
* to modify when adding or deleting a device
* ADD_A_DEVICE.
* There are several things to modify when adding or deleting a
* device.
  -->I will use the Compressor for a simple example.
* Here is the complete list.
*
*   The following are in DevicePriority.h
* 1) Need to create the FSM Priority table (au8_CompressorPriority)
* 2) Next you need to add the default state (au8DeviceDefaultState)
* 3) Add the device to the priority table (apau8_DevicePriorityTable)
* 4) Add the voting Device array (ai8VoteStateCompressor)
* 5) Add the voting data access array (asVoteDeviceStatus)
* 6) Need to create the deviceID (EDeviceID)
* 7) Add the Device Valid States (aSDeviceValidStatesTable_SEVS)
*
* You need to pay very special attention to the order in the tables
******************************************************************************/

/********************** Device Resolver Voting Data Array ********************/

// Device Resolver Voting Data Access Array:

static const VoteDeviceState asVoteDeviceStatus[(uint8_t)DEVICE_LastDevice] = {
    //  0 - Compressor
    {
        ai8_VoteStateCompressor,
        NUM_ELEMENTS(ai8_VoteStateCompressor) },

    //  1 - FrzFan
    {
        ai8_VoteStateFrzFan,
        NUM_ELEMENTS(ai8_VoteStateFrzFan) },

    //  2 - RefFan
    {
        ai8_VoteStateRefFan,
        NUM_ELEMENTS(ai8_VoteStateRefFan) },

    //  3 - CoolFan
    {
        ai8_VoteStateCoolFan,
        NUM_ELEMENTS(ai8_VoteStateCoolFan) },

    //  4 - RefDamper
    {
        ai8_VoteStateRefDamper,
        NUM_ELEMENTS(ai8_VoteStateRefDamper) },

    //  5 - RefVarDamper
    {
        ai8_VoteStateRefVarDamper,
        NUM_ELEMENTS(ai8_VoteStateRefVarDamper) },

    //  6 - VerticalBeamHeater
    {
        ai8_VoteStateVerticalBeamHeater,
        NUM_ELEMENTS(ai8_VoteStateVerticalBeamHeater) },

    //  7 - FrzDefHeater
    {
        ai8_VoteStateFrzDefHeater,
        NUM_ELEMENTS(ai8_VoteStateFrzDefHeater) },

    //  8 - RefDefHeater
    {
        ai8_VoteStateRefDefHeater,
        NUM_ELEMENTS(ai8_VoteStateRefDefHeater) },

    //  9 - Valve
    {
        ai8_VoteStateValve,
        NUM_ELEMENTS(ai8_VoteStateValve) },

    //  10 - IonGenerator
    {
        ai8_VoteStateIonGenerator,
        NUM_ELEMENTS(ai8_VoteStateIonGenerator) },

};

static const uint8_t au8_DeviceDefaultState[] = {
    (uint8_t)DS_Off, // Compressor
    (uint8_t)DS_Off, // FrzFan
    (uint8_t)DS_Off, // RefFan
    (uint8_t)DS_Off, // CoolFan
    (uint8_t)DS_Off, // RefDamper
    (uint8_t)DS_Off, // RefVarDamper
    (uint8_t)DS_Off, // VerticalBeamHeater
    (uint8_t)DS_Off, // FrzDefHeater
    (uint8_t)DS_Off, // RefDefHeater
    (uint8_t)MAX_ValveState - 1, // Valve
    (uint8_t)DS_Off, // IonGenerator
};

static TfpDeviceControlFunction afp_DeviceControlFunctions[] = {
    (TfpDeviceControlFunction)Driver_CompFreqSet,
    (TfpDeviceControlFunction)Set_FrzFanDuty,
    (TfpDeviceControlFunction)Set_RefFanDuty,
    (TfpDeviceControlFunction)Set_CoolFanDuty,
    (TfpDeviceControlFunction)Set_ActiveDamperState,
    (TfpDeviceControlFunction)Set_SlaveDamperState,
    (TfpDeviceControlFunction)Set_VerticalBeamHeaterState,
    (TfpDeviceControlFunction)Set_DefrostHeaterState,
    (TfpDeviceControlFunction)Set_RefHeaterState,
    (TfpDeviceControlFunction)Drive_ValveSetState,
    (TfpDeviceControlFunction)Set_IonGeneratorState,
};

// Resolved Device Status
uint8_t au8_ResolverDeviceStatus[DEVICE_LastDevice];
// Priority array of which currently device is in control
uint8_t au8_DeviceControllingFSM[DEVICE_LastDevice];

#endif
