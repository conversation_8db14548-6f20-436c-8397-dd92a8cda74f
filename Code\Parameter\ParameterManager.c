#include "ParameterManager.h"
#include "Driver_Flash.h"
#include "InverterUsart.h"
#include "FactoryMode.h"
#include "SystemManager.h"
#include "miio_api.h"
#include "syslog.h"

parameter_manager_st parameter_manager;
static bool b_cmd_wifi = false;

model_pid_st model_pid_maps[] = 
{
    {"bf11e", "28205", MACHINE_TYPE_FRENCH},
    {"bs39e", "28801", MACHINE_TYPE_CROSS},
};

static bool b_cmd_model = false;
static bool b_cmd_pid = false;
static bool b_cmd_mac = false;
static bool b_cmd_did = false;

static model_pid_st *SearchModelPid(uint8_t *model)
{
    uint8_t index;
    uint8_t num;

    num = sizeof(model_pid_maps) / sizeof(model_pid_st);

    for(index = 0; index < num; index++)
    {
        if(strcmp((const char *)model, (const char *)model_pid_maps[index].model) == 0)
        {
            return &model_pid_maps[index];
        }
    }
    return NULL;
}

static int8_t SetMainSn(sn_fireware_st *sf)
{
    if(WriteProductSn(sf->outsn, PRODUCT_SN_SIZE, true) == 0)
    {
        sf->complete = true;
    }
    return 0;
}

static int8_t GetMainSn(sn_fireware_st *sf)
{
    if(ReadProductSn(sf->insn, PRODUCT_SN_SIZE) < 0)
    {
        memset(sf->insn, 0, PRODUCT_SN_SIZE);
    }
    sf->readable = true;
    return 0;
}

static void SetModelResult(const char* pValue, char result)
{
    if(result > 0 && b_cmd_model == true)
    {
        parameter_manager.b_model_complete = true;
        parameter_manager.modeltimer = 0;
    }
    else
    {
        b_cmd_model = false;
        parameter_manager.modeltimer = 0;
    }
}

static void SetModelPidResult(const char* pValue, char result)
{
    if(result > 0 && b_cmd_pid == true)
    {
        parameter_manager.b_pid_complete = true;
        parameter_manager.modeltimer = 0;
    }
    else
    {
        b_cmd_pid = false;
        parameter_manager.modeltimer = 0;
    }
}

static void GetMacResult(const char* pValue, char result)
{
    static uint8_t error_count = 0;

    if(result > 0 && b_cmd_mac == true)
    {
        memset(parameter_manager.mac, 0, FIREWARE_PROPERTY_MAC_LEN);
        strncpy((char *)parameter_manager.mac, pValue, FIREWARE_PROPERTY_MAC_LEN - 1);
        if(strlen((char *)parameter_manager.mac) == FIREWARE_PROPERTY_MAC_LEN - 1)
        {
            parameter_manager.b_mac_complete = true;
            parameter_manager.modeltimer = 0;
            //SetNfcPropertyValue(NFC_PROPERTY_TYPE_MAC, parameter_manager.mac);
            return;
        }
        else if(error_count++ > WIFI_MODEL_RETRY_COUNT)
        {
            return;
        }
    }

    b_cmd_mac = false;
    parameter_manager.modeltimer = 0;
}

static void GetDidResult(const char* pValue, char result)
{
    static uint8_t error_count = 0;

    if(result > 0 && b_cmd_did == true)
    {
        memset(parameter_manager.did, 0, FIREWARE_PROPERTY_DID_LEN);
        strncpy((char *)parameter_manager.did, pValue, FIREWARE_PROPERTY_DID_LEN - 1);
        if(strlen((char *)parameter_manager.did) > 0 && strncmp((char *)parameter_manager.did, "error", 5) != 0)
        {
            parameter_manager.b_did_complete = true;
            parameter_manager.modeltimer = 0;
            //SetNfcPropertyValue(NFC_PROPERTY_TYPE_DID, parameter_manager.did);
            return;
        }
        else if(error_count++ > WIFI_MODEL_RETRY_COUNT)
        {
            return;
        }
    }

    b_cmd_did = false;
    parameter_manager.modeltimer = 0;
}

static int8_t CancleWifiSnAsync(sn_fireware_st *sf)
{
    b_cmd_wifi = false;
    return 0;
}

static void GetWifiSnResult(const char* pValue, char result)
{
    if(result > 0 && b_cmd_wifi == true)
    {
        if(CheckSnFormat((uint8_t *)pValue, PRODUCT_SN_SIZE) == true)
        {
            strcpy((char *)parameter_manager.sn_firewares[SN_FIREWARE_WIFI].insn, pValue);
        }
        else
        {
            memset(parameter_manager.sn_firewares[SN_FIREWARE_WIFI].insn, 0, PRODUCT_SN_SIZE);
        }
        parameter_manager.sn_firewares[SN_FIREWARE_WIFI].readable = true;
    }
    else
    {
        b_cmd_wifi = false;
    }
}

static void SetWifiSnResult(const char* pValue, char result)
{
    if(result > 0 && b_cmd_wifi == true)
    {
        parameter_manager.sn_firewares[SN_FIREWARE_WIFI].complete = true;
    }
    else
    {
        b_cmd_wifi = false;
    }
}

static int8_t SetWifiSn(sn_fireware_st *sf)
{
    if(b_cmd_wifi == false)
    {
        if(execute_wifi_cmd_async(WIFI_CMD_SN, SetWifiSnResult) == 0)
        {
            b_cmd_wifi = true;
        }
    }
    return 0;
}

static int8_t GetWifiSn(sn_fireware_st *sf)
{
    if(b_cmd_wifi == false)
    {
        if(execute_wifi_cmd_async(WIFI_CMD_GET_SN, GetWifiSnResult) == 0)
        {
            b_cmd_wifi = true;
        }
    }
    return 0;
}

static void CancleFirewareAsync(void)
{
    sn_fireware_e index;
    sn_fireware_st *sf;

    parameter_manager.b_sn_complete = false;
    parameter_manager.b_sn_readable = false;

    for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
    {
        sf = &parameter_manager.sn_firewares[index];
        if(sf->cancle_fireware_async != NULL)
        {
            sf->cancle_fireware_async(sf);
        }
        sf->complete = false;
        sf->readable = false;
    }
}

#ifndef USER_MODEL
static void ParameterManagerHandShake(void)
{
    sn_fireware_e index;
    sn_fireware_st *sf;

    parameter_manager.timercount++;
    parameter_manager.b_sn_readable = true;
    for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
    {
        sf = &parameter_manager.sn_firewares[index];
        sf->get_sn(sf);
        if(sf->readable == false)
        {
            parameter_manager.b_sn_readable = false;
        }
    }

    if(parameter_manager.b_sn_readable ||
       parameter_manager.timercount > HANDSHAKE_TIMEOUT_100MS)
    {
        parameter_manager.timercount = 0;
        for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
        {
            sf = &parameter_manager.sn_firewares[index];
            if(sf->insn[0] != 0)
            {
                if(parameter_manager.latest_sn[0] == 0)
                {
                    memcpy(parameter_manager.latest_sn, sf->insn, PRODUCT_SN_SIZE);
                }
                else
                {
                    if(memcmp(parameter_manager.latest_sn, sf->insn, PRODUCT_SN_SIZE) != 0)
                    {
                        memset(parameter_manager.latest_sn, 0, PRODUCT_SN_SIZE);
                        parameter_manager.fcode = index << 8 | PARAMETER_MANAGER_SN_CONFLICT_ERROR;
                        parameter_manager.state = PARAMETER_MANAGER_STATE_FAULT;
                        goto out;
                    }
                }
            }
        }

        if(parameter_manager.latest_sn[0] == 0)
        {
            parameter_manager.fcode = PARAMETER_MANAGER_SN_INVAILD_ERROR;
            parameter_manager.state = PARAMETER_MANAGER_STATE_FAULT;
            goto out;
        }
        parameter_manager.state = PARAMETER_MANAGER_STATE_SYNC;
        goto out;
    }
    return;
out:
    CancleFirewareAsync();
}

static void ParameterManagerSync(void)
{
    sn_fireware_e index;
    sn_fireware_st *sf;

    parameter_manager.timercount++;
    parameter_manager.b_sn_complete = true;
    if(parameter_manager.b_sn_force)
    {
        for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
        {
            sf = &parameter_manager.sn_firewares[index];
            memcpy(sf->outsn, parameter_manager.latest_sn, PRODUCT_SN_SIZE);
            sf->set_sn(sf);
            if(sf->complete == false)
            {
                parameter_manager.b_sn_complete = false;
            }
        }
    }
    else
    {
        for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
        {
            sf = &parameter_manager.sn_firewares[index];
            if(sf->insn[0] == 0)
            {
                memcpy(sf->outsn, parameter_manager.latest_sn, PRODUCT_SN_SIZE);
                sf->set_sn(sf);
                if(sf->complete == false)
                {
                    parameter_manager.b_sn_complete = false;
                }
            }
        }
    }

    if(parameter_manager.b_sn_complete ||
       parameter_manager.timercount > SYNC_TIMEOUT_100MS)
    {
        parameter_manager.timercount = 0;
        if(parameter_manager.match == false)
        {
            parameter_manager.b_sn_force = false;
            CancleFirewareAsync();
            if(parameter_manager.latest_sn[0] == 0)
            {
                parameter_manager.fcode = PARAMETER_MANAGER_SN_INVAILD_ERROR;
                parameter_manager.state = PARAMETER_MANAGER_STATE_FAULT;
            }
            else
            {
                parameter_manager.fcode = PARAMETER_MANAGER_SN_NONE;
                parameter_manager.state = PARAMETER_MANAGER_STATE_PROBE;
            }
        }
        else
        {
            err("modify the sn reboot\n");
            while(1);
        }
    }

}
#else
static void ParameterManagerHandShake(void)
{
    parameter_manager.state = PARAMETER_MANAGER_STATE_SYNC;
}

static void ParameterManagerSync(void)
{
    parameter_manager.state = PARAMETER_MANAGER_STATE_PROBE;
}

#endif

static void ParameterManagerProbe(void)
{
    parameter_manager.match = true;
    parameter_manager.state = PARAMETER_MANAGER_STATE_CHECK;
    memset(parameter_manager.model, 0, FIREWARE_PROPERTY_MODEL_LEN);
    ReadProductUserModel(parameter_manager.model, FIREWARE_PROPERTY_MODEL_LEN);
    //SetNfcPropertyValue(NFC_PROPERTY_TYPE_MODEL, parameter_manager.model);
}

static void ParameterManagerFault(void)
{

}

static void ParameterManagerCheck(void)
{

    sn_fireware_e index;
    sn_fireware_st *sf;
    FridgeState_t fstate;

    fstate = Get_FridgeState();
    if(parameter_manager.modeltimer < MODEL_TIMEOUT_100MS)
    {
        parameter_manager.modeltimer++;
    }
    else if(fstate != eFridge_Factory || Get_WifiFactoryMode())
    {
        if(b_cmd_model == false &&
           parameter_manager.b_model_complete == false)
        {
            if(execute_wifi_cmd_async(WIFI_CMD_SET_MODEL, SetModelResult) == 0)
            {
                b_cmd_model = true;
            }
        }

        if(b_cmd_pid == false &&
           parameter_manager.b_model_complete == true &&
           parameter_manager.b_pid_complete == false)
        {
            if(execute_wifi_cmd_async(WIFI_CMD_BLE_CONF, SetModelPidResult) == 0)
            {
                b_cmd_pid = true;
            }
        }

        if(b_cmd_mac== false &&
           parameter_manager.b_model_complete == true &&
           parameter_manager.b_pid_complete == true &&
           parameter_manager.b_mac_complete == false)
        {
            if(execute_wifi_cmd_async(WIFI_CMD_MAC, GetMacResult) == 0)
            {
                b_cmd_mac = true;
            }
        }

        if(b_cmd_did == false &&
           parameter_manager.b_model_complete == true &&
           parameter_manager.b_pid_complete == true &&
           parameter_manager.b_mac_complete == true &&
           parameter_manager.b_did_complete == false)
        {
            if(execute_wifi_cmd_async(WIFI_CMD_GETDID, GetDidResult) == 0)
            {
                b_cmd_did = true;
            }
        }
    }

}

void ParameterManagerRun(void)
{
    if(parameter_manager.state >= PARAMETER_MANAGER_STATE_HANDSHAKE
       && parameter_manager.state < PARAMETER_MANAGER_STATE_MAX)
    {
        if(parameter_manager.statefuncs[parameter_manager.state] != NULL)
        {
            parameter_manager.statefuncs[parameter_manager.state]();
        }
    }
}

bool IsParameterManagerReady(void)
{
    return parameter_manager.match;
}

bool IsParameterManagerFault(void)
{
    return (parameter_manager.fcode != PARAMETER_MANAGER_SN_NONE &&
            parameter_manager.state == PARAMETER_MANAGER_STATE_FAULT);
}

void GetParameterManagerFault(uint8_t *fault, uint8_t size)
{
    uint8_t index;

    if(size < PRODUCT_SN_SIZE)
    {
        return;
    }

    switch(parameter_manager.fcode & 0xFF)
    {
    case PARAMETER_MANAGER_SN_CONFLICT_ERROR:
        index = (parameter_manager.fcode >> 8) & 0xFF;
        sprintf((char *)fault, "SN:CONFILCT%d", index);
        break;
    case PARAMETER_MANAGER_SN_INVAILD_ERROR:
        strcpy((char *)fault, "SN:INVAILD");
        break;
    case PARAMETER_MANAGER_SN_MATCH_ERROR:
        strcpy((char *)fault, "SN:NOT MATCH");
        break;
    default:
        strcpy((char *)fault, "SN:UNKOWN");
        break;
    }
    return;
}

#ifndef USER_MODEL
void ParameterSnUpdate(void)
{
    sn_fireware_e index;
    sn_fireware_st *sf;

    if(ReadProductSn(parameter_manager.latest_sn, PRODUCT_SN_SIZE) < 0)
    {
        memset(parameter_manager.latest_sn, 0, PRODUCT_SN_SIZE);
    }
    parameter_manager.state = PARAMETER_MANAGER_STATE_SYNC;
    for(index = SN_FIREWARE_MAIN; index < SN_FIREWARE_MAX; index++)
    {
        sf = &parameter_manager.sn_firewares[index];
        sf->complete = false;
    }
    CancleFirewareAsync();
    parameter_manager.timercount = 0;
    parameter_manager.b_sn_force = true;
}
#else
void ParameterSnUpdate(void)
{

}
#endif

void ParameterManagerInit(void)
{
    sn_fireware_st *sf;

    memset(&parameter_manager, 0, sizeof(parameter_manager_st));
    sf = &parameter_manager.sn_firewares[SN_FIREWARE_MAIN];
    sf->set_sn = SetMainSn;
    sf->get_sn = GetMainSn;
    sf = &parameter_manager.sn_firewares[SN_FIREWARE_WIFI];
    sf->set_sn = SetWifiSn;
    sf->get_sn = GetWifiSn;
    sf->cancle_fireware_async = CancleWifiSnAsync;
    parameter_manager.statefuncs[PARAMETER_MANAGER_STATE_HANDSHAKE] = ParameterManagerHandShake;
    parameter_manager.statefuncs[PARAMETER_MANAGER_STATE_SYNC] = ParameterManagerSync;
    parameter_manager.statefuncs[PARAMETER_MANAGER_STATE_PROBE] = ParameterManagerProbe;
    parameter_manager.statefuncs[PARAMETER_MANAGER_STATE_CHECK] = ParameterManagerCheck;
    parameter_manager.statefuncs[PARAMETER_MANAGER_STATE_FAULT] = ParameterManagerFault;
}

uint8_t GetMachineType(void)
{
    uint8_t model[PRODUCT_MODEL_BYTES+1] = {0};
    model_pid_st *pm;

    //ReadProductModel(model, PRODUCT_MODEL_BYTES);
	ReadProductModel_508Pro(model, PRODUCT_MODEL_BYTES);
    pm = SearchModelPid(model);
    if(NULL == pm)
    {
        return MACHINE_TYPE_UNKOWN;
    }
    return pm->mtype;
}

uint8_t *GetMachinePid(void)
{
    uint8_t model[PRODUCT_MODEL_BYTES+1] = {0};
    model_pid_st *pm;

#ifdef BLE_PID
    return (uint8_t *)BLE_PID;
#else
    ReadProductModel(model, PRODUCT_MODEL_BYTES);
    pm = SearchModelPid(model);
    if(NULL == pm)
    {
        return NULL;
    }
    return pm->pid;
#endif
}

void GetParameterSn(sn_fireware_e index, uint8_t *sn, uint8_t size)
{
    uint8_t *fsn;

    if(index >= SN_FIREWARE_MAX || size < PRODUCT_SN_SIZE)
    {
       strcpy((char *)sn, "SN:ERROR");
       return;
    }
    fsn = parameter_manager.sn_firewares[index].insn;
    if(CheckSnFormat(fsn, PRODUCT_SN_SIZE))
    {
        memcpy(sn, fsn, PRODUCT_SN_SIZE);
    }
    else
    {
        strcpy((char *)sn, "SN:INVAILD");
    }
}

