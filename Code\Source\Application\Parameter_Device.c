/*!
 * @file
 * @brief Manages all the state variables of the cooling controller.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Parameter_Device.h"
#include "Parameter_TemperatureZone.h"
#include "CoolingCycle.h"
#include "Driver_Fan.h"
#include "Core_Types.h"
#include "FridgeRunner.h"
#include "FactoryMode.h"
#include "ParameterManager.h"
#include "SystemManager.h"



#define U8_DOWN_LOAD_TIME_RANGE_NUMBER ((uint8_t)10)

static CoolingOutputState_t coolingOutputState;
static uint8_t u8_FrzFanAdjust;
static uint8_t u8_RefFanAdjust;
static uint8_t u8_CoolFanAdjust;
static uint8_t u8_powersave_freq = FREQ_0HZ;
static uint8_t u8_noisereduce_freq = FREQ_0HZ;

// 正常压机频率
static const uint8_t ary_NormalCompRevIndex[][U8_FRZ_LEVEL_LENGTH] = {
    //   -24,      -23,          -22,       -21,         -20,       -19,        -18,        -17,        -16    // COMP ON TIME(MINUTE)
    { FREQ_70HZ, FREQ_70HZ, FREQ_70HZ, FREQ_70HZ, FREQ_70HZ, FREQ_45HZ, FREQ_45HZ, FREQ_45HZ, FREQ_45HZ }, // RT <=  13
    { FREQ_80HZ, FREQ_80HZ, FREQ_80HZ, FREQ_70HZ, FREQ_70HZ, FREQ_70HZ, FREQ_70HZ, FREQ_45HZ, FREQ_45HZ }, // 13 < RT <= 18
    { FREQ_118HZ, FREQ_108HZ, FREQ_80HZ, FREQ_80HZ, FREQ_80HZ, FREQ_80HZ, FREQ_80HZ, FREQ_70HZ, FREQ_70HZ }, // 18 < RT <= 23
    { FREQ_118HZ, FREQ_108HZ, FREQ_80HZ, FREQ_80HZ, FREQ_80HZ, FREQ_80HZ, FREQ_80HZ, FREQ_70HZ, FREQ_70HZ }, // 23 < RT <= 28
    { FREQ_118HZ, FREQ_118HZ, FREQ_108HZ, FREQ_108HZ, FREQ_80HZ, FREQ_80HZ, FREQ_80HZ, FREQ_80HZ, FREQ_80HZ }, // 28 < RT <= 35
    { FREQ_136HZ, FREQ_136HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_108HZ, FREQ_108HZ }, // 35 < RT <= 40
    { FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_136HZ, FREQ_136HZ }, // RT > 40
};

// 高负载压机频率
static const uint8_t ary_HighLoadCompRevIndex[][U8_FRZ_LEVEL_LENGTH] = {
    //   -24,      -23,          -22,       -21,         -20,       -19,        -18,        -17,        -16    // COMP ON TIME(MINUTE)
    { FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_80HZ, FREQ_80HZ, FREQ_80HZ, FREQ_80HZ, FREQ_80HZ }, // RT <=  13
    { FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_80HZ, FREQ_80HZ }, // 13 < RT <= 18
    { FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_108HZ, FREQ_108HZ }, // 18 < RT <= 23
    { FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_108HZ, FREQ_108HZ }, // 23 < RT <= 28
    { FREQ_136HZ, FREQ_136HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_118HZ, FREQ_108HZ, FREQ_108HZ }, // 28 < RT <= 35
    { FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_118HZ, FREQ_118HZ }, // 35 < RT <= 40
    { FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_136HZ, FREQ_136HZ }, // RT > 40
};

// 快速运转压机频率
static const uint8_t ary_FastRunCompRevIndex[][U8_FRZ_LEVEL_LENGTH] = {
    //   -24,      -23,          -22,       -21,         -20,       -19,        -18,        -17,        -16    // COMP ON TIME(MINUTE)
    { FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ }, // RT <=  13
    { FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ, FREQ_108HZ }, // 13 < RT <= 18
    { FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_118HZ, FREQ_118HZ }, // 18 < RT <= 23
    { FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_118HZ, FREQ_118HZ }, // 23 < RT <= 28
    { FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ }, // 28 < RT <= 35
    { FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ, FREQ_136HZ }, // 35 < RT <= 40
    { FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ, FREQ_147HZ }, // RT > 40
};

static const uint8_t ary_CompOffFrzFanDuty[(uint8_t)RT_MAXSIZE] = {
    75, // 9V
    75, // 9V
    83, // 10V
    83, // 10V
    83, // 10V
    100, // 12V
    100 // 12V
};

static const uint8_t ary_CompOnFrzFanDuty[FREQ_MAX] = {
    0,
    50, // 8V
    50, // 8V
    59, // 9V
    59, // 9V	//1650
    59, // 9V	//1800	//1650
    59, // 9V
    59, // 9V
    69, // 10V
    69, // 10V
    77, // 11V
    100  // 12V	//77  // 11V
};

static const uint8_t ary_CompOnRefFanDuty[FREQ_MAX] = {
    0,
    50, // 8V
    50, // 8V
    59, // 9V
    50, // 8V	//1650
    50, // 8V	//1800	//1650
    59, // 9V
    59, // 9V
    69, // 10V
    77, // 11V
    77, // 11V
    77  // 11V
};

static const uint8_t ary_CondFanDuty[FREQ_MAX] = {
    0,
    30, // FREQ_41HZ
    35, // FREQ_45HZ
    40, // FREQ_54HZ
    55, // FREQ_55HZ - 1650		
    55, // FREQ_60HZ - 1800	//1650	FREQ_55HZ	// FREQ_57HZ	
    40, // FREQ_70HZ
    45, // FREQ_80HZ
    45, // FREQ_108HZ
    50, // FREQ_118HZ
    55, // FREQ_136HZ
    60 // FREQ_147HZ	//55 // FREQ_147HZ
};

uint8_t Get_CompFreqIndex(CoolingCapacityState_t state, uint16_t compOnMinute)
{
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint8_t frz_temp_index = 0;
    uint8_t frz_temp_set = Get_FrzSetTemp();
    uint8_t u8_index = 0;
    uint8_t freq_index;
    uint8_t normal_freq_index;
    bool pull_down_state = false;
    bool turbo_freeze = false;
    bool cool_fan_error = Is_FanError(COOL_FAN);
    bool cooling_first = false;
    uint8_t fridge_type = Get_FridgeType();

    if(frz_temp_set < U8_FRZ_ON_OFFLEVEL_MIN)
    {
        frz_temp_index = 0;
    }
    else if(frz_temp_set > U8_FRZ_LEVEL_MAX)
    {
        frz_temp_index = U8_FRZ_LEVEL_MAX - 6;
    }
    else
    {
        frz_temp_index = frz_temp_set - 6;
    }

    switch(state)
    {
        case(CoolingCapacityState_t)eCoolingCapacity_Normal:
            if(compOnMinute > U8_IMPROVE_FREQ_TIME_MINUTE)
            {
                freq_index = ary_HighLoadCompRevIndex[room_range][frz_temp_index];
                coolingOutputState = eOutput_HighLoad;
            }
            else
            {
                freq_index = ary_NormalCompRevIndex[room_range][frz_temp_index];
                coolingOutputState = eOutput_Normal;
            }
            break;
        case(CoolingCapacityState_t)eCoolingCapacity_FridgePowerOn:
            pull_down_state = Get_PowerOnPullDownState();
            turbo_freeze = Get_TurboFreezeState();

            if(true == pull_down_state)
            {
                //����ͳһ
                if(compOnMinute < 60)
                {
                    freq_index = FREQ_70HZ;
                }
                else if(compOnMinute < 120)
                {
                    freq_index = FREQ_80HZ;
                }
                else if(compOnMinute < 180)
                {
                    freq_index = FREQ_108HZ;
                }
                else if(compOnMinute < 240)
                {
                    freq_index = FREQ_118HZ;
                }
                else
                {
                    freq_index = FREQ_136HZ;
                }
                
            }
            else
            {
                freq_index = ary_NormalCompRevIndex[room_range][frz_temp_index];
            }

            if(true == turbo_freeze)
            {
                freq_index = FREQ_136HZ;
            }
            coolingOutputState = eOutput_FridgePowerOn;
            break;
        case(CoolingCapacityState_t)eCoolingCapacity_HighLoad:
            if(compOnMinute > U8_IMPROVE_FREQ_TIME_MINUTE)
            {
                freq_index = ary_FastRunCompRevIndex[room_range][frz_temp_index];
                coolingOutputState = eOutput_FastRunning;
            }
            else
            {
                freq_index = ary_HighLoadCompRevIndex[room_range][frz_temp_index];
                coolingOutputState = eOutput_HighLoad;
            }
            break;
        case(CoolingCapacityState_t)eCoolingCapacity_FastRunning:
            freq_index = ary_FastRunCompRevIndex[room_range][frz_temp_index];
            coolingOutputState = eOutput_FastRunning;
            break;
        case(CoolingCapacityState_t)eCoolingCapacity_EnergyMode:
            normal_freq_index = ary_NormalCompRevIndex[room_range][frz_temp_index];
            if(0 == Get_CoolingCycleNumber())
            {
                cooling_first = true;
            }

            if(RT_BELOW18 == room_range)
            {
                if(compOnMinute < 120 || cooling_first)
                {
                    freq_index = FREQ_41HZ;
                }
                else if(compOnMinute < 300)
                {
                    freq_index = FREQ_41HZ;
                }
                else
                {
                    freq_index = FREQ_54HZ;
                }
            }
            else
            {
                if(MACHINE_TYPE_FRENCH == fridge_type)
                {
                    if(compOnMinute < 120 || cooling_first)
                    {
                        freq_index = FREQ_60HZ;
                    }
                    else if(compOnMinute < 300)
                    {
                        freq_index = FREQ_60HZ;
                    }
                    else
                    {
                        freq_index = FREQ_70HZ;
                    }
                }
                else
                {
                    if(compOnMinute < 120 || cooling_first)
                    {
                        freq_index = FREQ_60HZ;
                    }
                    else if(compOnMinute < 300)
                    {
                        freq_index = FREQ_60HZ;
                    }
                    else
                    {
                        freq_index = FREQ_70HZ;
                    }
                }
            }

            if(freq_index > normal_freq_index)
            {
                freq_index = normal_freq_index;
            }

            coolingOutputState = eOutput_EnergyMode;
            break;
        case(CoolingCapacityState_t)eCoolingCapacity_FactoryCompleted:
            freq_index = FREQ_108HZ;
            coolingOutputState = eOutput_FactoryCompleted;
            break;
        default:
            break;
    }

    if(true == cool_fan_error)
    {
        if(freq_index > FREQ_80HZ)
        {
            freq_index = FREQ_80HZ;
        }
    }

    return (freq_index);
}

uint8_t Get_CompOffFanSettingIndex(RoomTempRange_t range)
{
    uint8_t fan_duty = ary_CompOffFrzFanDuty[range];
    return (fan_duty);
}

uint8_t Get_CondFanSettingIndex(uint8_t freqIndex)
{
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint8_t fan_duty_index = 0;
    CoolingCapacityState_t state = Get_CoolingCapacityState();
    uint16_t run_time = Get_CompStillOnTimeMinute();
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    uint16_t fct_room_temp = Get_FactoryRoomTemp();

    if(((CoolingCapacityState_t)eCoolingCapacity_FactoryCompleted == state) &&
        (run_time <= U8_FACTORY_COMPLETED_RUN_TIME) &&
        (fct_room_temp <= CON_32P0_DEGREE)
        )
    {
        fan_duty_index = 0;
    }
    else if(room_range >= (RoomTempRange_t)RT_BELOW18)
    //if(room_range >= (RoomTempRange_t)RT_BELOW18)
    {
        if(0 == freqIndex)
        {
            fan_duty_index = 0;
        }
        else if(0 != u8_CoolFanAdjust)
        {
            fan_duty_index = u8_CoolFanAdjust == MAX_UINT8 ? 0 : u8_CoolFanAdjust;
        }
        else
        {
            fan_duty_index = ary_CondFanDuty[freqIndex];
            //能耗模式，风机占空比
            if(b_energy_mode == true)
            {
                //fan_duty_index = 40;
            }
        }
    }
    return (fan_duty_index);
}

uint8_t Get_CompOnFrzFanSettingIndex(uint8_t freqIndex)
{
    uint8_t fan_duty = 0;
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    RoomTempRange_t room_range = Get_RoomTempRange();

    if(0 == freqIndex)
    {
        fan_duty = 0;
    }
    else if(0 != u8_FrzFanAdjust)
    {
        fan_duty = u8_FrzFanAdjust == MAX_UINT8 ? 0 : u8_FrzFanAdjust;
    }
    else
    {
        fan_duty = ary_CompOnFrzFanDuty[freqIndex];
        //能耗模式，风机占空比
        if(b_energy_mode == true && room_range == RT_BELOW18)
        {
            //fan_duty = 50;
        }
        else if(b_energy_mode == true && room_range == RT_BELOW35)
        {
            //fan_duty = 59;
        }
    }
    return (fan_duty);	
}

uint8_t Get_CompOnRefFanSettingIndex(uint8_t freqIndex)
{
    uint8_t fan_duty = 0;
    bool b_energy_mode = Get_EnergyConsumptionModeState();
    RoomTempRange_t room_range = Get_RoomTempRange();

    if(0 == freqIndex)
    {
        fan_duty = 0;
    }
    else if(0 != u8_RefFanAdjust)
    {
        fan_duty = u8_RefFanAdjust;
    }
    else
    {
        fan_duty = ary_CompOnRefFanDuty[freqIndex];
        if(b_energy_mode == true)
        {
            //能耗模式，风机占空比50%
            //fan_duty = 50;
        }
    }
    return (fan_duty);
}


CoolingOutputState_t Get_CoolingOutputState(void)
{
    return (coolingOutputState);
}

void Set_FrzFanAdjustParm(uint8_t adjust_value)
{
    u8_FrzFanAdjust = adjust_value;
}

uint16_t Get_FrzFanAdjustParm(void)
{
    return (u8_FrzFanAdjust);
}

void Set_CoolFanAdjustParm(uint8_t adjust_value)
{
    u8_CoolFanAdjust = adjust_value;
}

uint16_t Get_CoolFanAdjustParm(void)
{
    return (u8_CoolFanAdjust);
}

void Set_RefFanAdjustParm(uint8_t adjust_value)
{
    u8_RefFanAdjust = adjust_value;
}

uint16_t Get_RefFanAdjustParm(void)
{
    return (u8_RefFanAdjust);
}

uint8_t Get_CompOnPowerSaveFreq(void)
{
    return u8_powersave_freq;
}

uint8_t Get_CompOnNoiseReduceFreq(void)
{
    return u8_noisereduce_freq;
}

