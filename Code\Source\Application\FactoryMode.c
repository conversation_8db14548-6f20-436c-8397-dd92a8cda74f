/*!
 * @file
 * @brief Manages all the state variables of the factory mode.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "FactoryMode.h"
#include "Core_CallBackTimer.h"
#include "ResolverDevice.h"
#include "Driver_GradualLamp.h"
#include "IO_Device.h"
#include "Driver_DoubleDamper.h"
#include "Driver_CompFrequency.h"
#include "SystemManager.h"
#include "Driver_Fan.h"
#include "DisplayInterface.h"
#include "DisplayUsart.h"
#include "Driver_AdSample.h"
#include "LedController.h"
#include "miio_api.h"
#include "FridgeRunner.h"
#include "Drive_Valve.h"
#include "Iot_Spec.h"
#include "Driver_Flash.h"
#include "InverterUsart.h"
#include "ParameterManager.h"
#include "Driver_DoorSwitch.h"
#include "FaultCode.h"


#define U16_FACTORY_CHECK_CYCLE_SECOND (uint16_t)1

static st_CoreCallbackTimer st_FactoryModeTimer;
static uint16_t u16_ControlCount;
static uint16_t fct_room_temp = 0;
static bool b_FctCtrlLoadOver;
static bool b_FctWifiLedState = false;
static bool b_WifiFactory;
static bool b_WifiReboot;
static bool b_WifiConnected = false;
static bool b_WiFiMatch = false;
static bool b_WiFiMatchError = false;
static bool b_WiFiStateUpdate = true;
static bool b_FctWriteSnSuccess;
static bool b_WifiFactoryResponsed = false;
static bool b_WifiRebootResponsed  = false;

static uint8_t u8_FactoryDeviceFaultByte;
static uint8_t u8_FactoryDeviceFaultByte1;
static bool b_DefrostHeaterOn;
static uint16_t u16_FactoryTotalPower;
bool b_DefHeaterError = false;
char factory_data[FACTORY_DATA_BUFFER];

/*static const DevicePower_st ary_DevicePowerLimit[(uint8_t)FactoryDevice_Max] = {
    // High Low
    { 580, 420 }, // FactoryDevice_FrzDefHeater
    { 130, 100 }, // FactoryDevice_VBHeater 10w+-3% 12W
    { 90, 30 }, // FactoryDevice_RefLamp
    { 18, 8 }, // FactoryDevice_FrzLamp
    { 60, 20 }, // FactoryDevice_FrzFan
    { 50, 13 }, // FactoryDevice_CondFan
    { 18, 7 }, // FactoryDevice_Damper
};*/

static const DevicePower_st ary_DevicePowerLimit[(uint8_t)FactoryDevice_Max] = {
    // High Low
    { 580,  420 },  // FactoryDevice_FrzDefHeater
    { 130,  100 },  // FactoryDevice_VBHeater 10w+-3% 12W
    { 90,   30  },  // FactoryDevice_RefLamp
    { 18,   8   },  // FactoryDevice_FrzLamp
    { 60,   20  },  // FactoryDevice_FrzFan
    { 60,   20  },  // FactoryDevice_CondFan
    { 18,   7   },  // FactoryDevice_Damper
    { 130,  100 },  // FactoryDevice_Valve 5-22W()
    { 60,   20  },  // FactoryDevice_RefFan()
};

static void check_fault_code(void *data)
{
    uint32_t *perror = (uint32_t *)data;
    uint32_t error0 = Get_FaultCodeByte(eFCode_IotByte0);
    uint32_t error1 = Get_FaultCodeByte(eFCode_IotByte1);
    uint32_t error2 = Get_FaultCodeByte(eFCode_IotByte2);
    uint32_t error3 = Get_FaultCodeByte(eFCode_IotByte3);

    *perror = error0 + (error1 << 8) + (error2 << 16) + (error3 << 24);
}

static void check_door_state(void *data)
{
    uint8_t *door_alarm = (uint8_t *)data;
    uint8_t DoorStateRL = Get_DoorSwitchflagState(DOOR_REF_LEFT);
    uint8_t DoorStateRR = Get_DoorSwitchflagState(DOOR_REF_RIGHT);
    uint8_t DoorStateFL = Get_DoorSwitchflagState(DOOR_FRZ_LEFT);
    uint8_t DoorStateFR = Get_DoorSwitchflagState(DOOR_FRZ_RIGHT);

    *door_alarm = 0;

    if(DoorStateRL & 0x08)
    {
        *door_alarm |= 1 << 1;
    }

    if(DoorStateRR & 0x08)
    {
        *door_alarm |= 1 << 2;
    }

    if(DoorStateFL & 0x08)
    {
        *door_alarm |= 1 << 3;
    }

    if(DoorStateFR & 0x08)
    {
        *door_alarm |= 1 << 4;
    }
}

static void check_ref_temp(void *data)
{
    uint16_t temp;
    uint16_t *rt = (uint16_t *)data;

    temp = Get_SensorValue(SENSOR_REF);
    *rt = temp;
}

static void check_frz_temp(void *data)
{
    uint16_t temp;
    uint16_t *rt = (uint16_t *)data;

    temp = Get_SensorValue(SENSOR_FRZ);
    *rt = temp;
}

static void check_var_temp(void *data)
{
    uint16_t temp;
    uint16_t *rt = (uint16_t *)data;

    temp = Get_SensorValue(SENSOR_VV);
    *rt = temp;
}

static void check_def_temp(void *data)
{
    uint16_t temp;
    uint16_t *rt = (uint16_t *)data;

    temp = Get_SensorValue(SENSOR_DEFROST);
    *rt = temp;
}

static void check_env_temp(void *data)
{
    uint16_t temp;
    uint16_t *rt = (uint16_t *)data;

    temp = Get_SensorValue(SENSOR_ROOM);
    *rt = temp;
}

static void check_env_hum(void *data)
{
    uint8_t var;
    uint8_t *rt = (uint8_t *)data;

    var = Get_HumidityRange();
    *rt = var;
}

static void check_door_switch(void *data)
{
    uint8_t *door_switch = (uint8_t *)data;
    uint8_t DoorStateRL = Get_DoorSwitchflagState(DOOR_REF_LEFT);
    uint8_t DoorStateRR = Get_DoorSwitchflagState(DOOR_REF_RIGHT);
    uint8_t DoorStateFL = Get_DoorSwitchflagState(DOOR_FRZ_LEFT);
    uint8_t DoorStateFR = Get_DoorSwitchflagState(DOOR_FRZ_RIGHT);

    *door_switch = 0;

    if(DoorStateRL & 0x01)
    {
        *door_switch |= 1 << 1;
    }

    if(DoorStateRR & 0x01)
    {
        *door_switch |= 1 << 2;
    }

    if(DoorStateFL & 0x01)
    {
        *door_switch |= 1 << 3;
    }

    if(DoorStateFR & 0x01)
    {
        *door_switch |= 1 << 4;
    }
}

static void check_comp_error(void *data)
{
    uint8_t var;
    uint8_t *rt = (uint8_t *)data;

    var = Get_CompErrorState();
    *rt = var;
}

static void check_system_state(void *data)
{
    uint8_t var;
    uint8_t *rt = (uint8_t *)data;

    var = Get_FridgeState();
    *rt = var;
}

static void check_comp_feedfreq(void *data)
{
    uint8_t var;
    uint8_t *rt = (uint8_t *)data;

    var = Get_CompFeedbackFreq();
    *rt = var;
}

static void check_comp_feedpower(void *data)
{
    uint16_t var;
    uint16_t *rt = (uint16_t *)data;

    var = Get_CompPower();
    *rt = var;
}

static void check_comp_feedvol(void *data)
{
    uint16_t var;
    uint16_t *rt = (uint16_t *)data;

    var = Get_CompBusVoltage();
    *rt = var;
}

static void check_other_fault(void *data)
{
    uint32_t *perror = (uint32_t *)data;
    uint32_t error0 = u8_FactoryDeviceFaultByte;
    uint32_t error1 = u8_FactoryDeviceFaultByte1;//0
    uint32_t error2 = 0;
    uint32_t error3 = 0;

    *perror = error0 + (error1 << 8) + (error2 << 16) + (error3 << 24);
}

static void check_uint_power(void *data)
{
    uint16_t var;
    uint16_t *rt = (uint16_t *)data;

    var = u16_FactoryTotalPower;
    *rt = var;
}

static factory_item_st fitems[] = {
    { "2_1", FACTORY_ITEM_VAL_UINT32, check_fault_code },
    { "2_3", FACTORY_ITEM_VAL_UINT8, check_door_state },
    { "3_1", FACTORY_ITEM_VAL_UINT16, check_ref_temp },
    { "4_1", FACTORY_ITEM_VAL_UINT16, check_frz_temp },
    { "5_1", FACTORY_ITEM_VAL_UINT16, check_var_temp },
    { "10_2", FACTORY_ITEM_VAL_UINT16, check_def_temp },
    { "10_9", FACTORY_ITEM_VAL_UINT16, check_env_temp },
    { "10_10", FACTORY_ITEM_VAL_UINT8, check_env_hum },
    { "10_11", FACTORY_ITEM_VAL_UINT8, check_door_switch },
    { "10_12", FACTORY_ITEM_VAL_UINT8, check_comp_error },
    { "10_13", FACTORY_ITEM_VAL_UINT8, check_system_state },
    { "10_20", FACTORY_ITEM_VAL_UINT8, check_comp_feedfreq },
    { "10_21", FACTORY_ITEM_VAL_UINT16, check_uint_power },
    { "10_22", FACTORY_ITEM_VAL_UINT16, check_comp_feedvol },
    { "10_36", FACTORY_ITEM_VAL_UINT32, check_other_fault },
};

static void Check_Factory_Items(void)
{
    uint16_t items = sizeof(fitems) / sizeof(factory_item_st);
    uint8_t buf[PRODUCT_MODEL_SIZE + 1] = { 0 };
    factory_item_st *fit = NULL;
    uint16_t index;
    uint32_t udata32;
    uint8_t udata8;
    int8_t data8;
    uint16_t udata16;

    memset(factory_data, 0, sizeof(factory_data));

    str_n_cat(factory_data, 1, "\"");

    if(ReadProductUserModel(buf, PRODUCT_MODEL_SIZE) < 0)
    {
        goto out;
    }

    str_n_cat(factory_data, 3, "0:", buf, " ");
    for(index = 0; index < items; index++)
    {
        fit = &fitems[index];

        if(fit->type == FACTORY_ITEM_VAL_UINT32)
        {
            udata32 = 0;
            fit->check_factory_item(&udata32);
            snprintf((char *)buf, PRODUCT_MODEL_SIZE, "%u", udata32);
        }
        else if(fit->type == FACTORY_ITEM_VAL_UINT8)
        {
            udata8 = 0;
            fit->check_factory_item(&udata8);
            snprintf((char *)buf, PRODUCT_MODEL_SIZE, "%u", udata8);
        }
        else if(fit->type == FACTORY_ITEM_VAL_INT8)
        {
            data8 = 0;
            fit->check_factory_item(&data8);
            snprintf((char *)buf, PRODUCT_MODEL_SIZE, "%d", data8);
        }
        else if(fit->type == FACTORY_ITEM_VAL_UINT16)
        {
            udata16 = 0;
            fit->check_factory_item(&udata16);
            snprintf((char *)buf, PRODUCT_MODEL_SIZE, "%u", udata16);
        }

        if(index + 1 == items)
        {
            str_n_cat(factory_data, 3, fit->key, ":", buf);
        }
        else
        {
            str_n_cat(factory_data, 4, fit->key, ":", buf, " ");
        }
    }
out:
    str_n_cat(factory_data, 1, "\"");
}


static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_FactoryModeTimer);
}

static void wifi_factory_response(const char* pValue, char result)
{
    if(result > 0 && b_WifiFactory == true)
    {
        b_WifiFactoryResponsed = true;
    }
    else
    {
        b_WifiFactory = false;
    }
}

static void wifi_reboot_response(const char* pValue, char result)
{
    if(result > 0 && b_WifiReboot == true)
    {
        b_WifiRebootResponsed = true;
    }
    else
    {
        b_WifiReboot = false;
    }
}

static void FactoryMode_ControlLoad(void)
{
    uint16_t u16_12V_power = Get_12VPower();
    uint16_t u16_220V_ad = Get_220VAdValue();
    uint16_t u16_power_low = 0;
    uint16_t u16_power_high = 0;
    bool wifi_error = false;
    bool wifi_match = false;

    wifi_match = is_wifi_arch_platform();
    
    if(u16_ControlCount < 1)
    {
        b_DefHeaterError = true;
        b_DefrostHeaterOn = false;
        u8_FactoryDeviceFaultByte = 0;
        u8_FactoryDeviceFaultByte1 = 0;
        //execute_wifi_cmd_async(WIFI_CMD_FACTORY, NULL);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, 0);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_Off);
        ForcedCtrl_DoubleDamper(false);
    }
    else if(u16_ControlCount < 6)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_On);
        b_DefrostHeaterOn = true;

        u16_power_low = ary_DevicePowerLimit[(uint8_t)FactoryDevice_FrzDefHeater].u16_LowPowerLimit;
        u16_power_high = ary_DevicePowerLimit[(uint8_t)FactoryDevice_FrzDefHeater].u16_HighPowerLimit;
        if((u16_220V_ad > u16_power_high) || (u16_220V_ad < u16_power_low))
        {
            b_DefHeaterError = false;
        }
    }
    else if(u16_ControlCount < 11)
    { // Sample after stabilization
        if(true == b_DefHeaterError)
        {
            u8_FactoryDeviceFaultByte |= 0x01;
        }
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_Off);
        b_DefrostHeaterOn = false;
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_On);

        if(u16_ControlCount >= 9 )// Sample after stabilization
        {
            u16_power_low = ary_DevicePowerLimit[(uint8_t)FactoryDevice_VBHeater].u16_LowPowerLimit;
            u16_power_high = ary_DevicePowerLimit[(uint8_t)FactoryDevice_VBHeater].u16_HighPowerLimit;
            if((u16_12V_power > u16_power_high) || (u16_12V_power < u16_power_low))
            {
                u8_FactoryDeviceFaultByte |= 0x02;
            }
        }
    }
    else if(u16_ControlCount < 19)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_Off);
        if(u16_ControlCount == 11)
        {
            Drive_ValveForce(true);
        }

        if(u16_ControlCount >= 16)// Sample after stabilization
        {
            u16_power_low = ary_DevicePowerLimit[(uint8_t)FactoryDevice_Valve].u16_LowPowerLimit;
            u16_power_high = ary_DevicePowerLimit[(uint8_t)FactoryDevice_Valve].u16_HighPowerLimit;
            if((u16_12V_power > u16_power_high) || (u16_12V_power < u16_power_low))
            {
                u8_FactoryDeviceFaultByte1 |= 0x02;	//电动阀故障 - 新增
            }
        }
    }
    else if(u16_ControlCount < 24)
    {
        Drive_ValveForce(false);
        //Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 100);
        Test_FanCtrl(REF_FAN, true);

        if(u16_ControlCount >= 22)// Sample after stabilization
        {
            u16_power_low = ary_DevicePowerLimit[(uint8_t)FactoryDevice_RefFan].u16_LowPowerLimit;
            u16_power_high = ary_DevicePowerLimit[(uint8_t)FactoryDevice_RefFan].u16_HighPowerLimit;
            if((u16_12V_power > u16_power_high) || (u16_12V_power < u16_power_low))
            {
                u8_FactoryDeviceFaultByte1 |= 0x01;	//冷藏风机功率故障 - 新增
            }
        }
    }
    else if(u16_ControlCount < 29)
    {
        //Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 0);
        //Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 100);
        Test_FanCtrl(REF_FAN, false);
        Test_FanCtrl(FRZ_FAN, true);

        if(u16_ControlCount >= 27)// Sample after stabilization
        {
            u16_power_low = ary_DevicePowerLimit[(uint8_t)FactoryDevice_FrzFan].u16_LowPowerLimit;
            u16_power_high = ary_DevicePowerLimit[(uint8_t)FactoryDevice_FrzFan].u16_HighPowerLimit;
            if((u16_12V_power > u16_power_high) || (u16_12V_power < u16_power_low))
            {
                u8_FactoryDeviceFaultByte |= 0x08;
            }
        }
    }
    else if(u16_ControlCount < 34)
    {
        //Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 0);
        //Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 100);
        Test_FanCtrl(FRZ_FAN, false);
        Test_FanCtrl(COOL_FAN, true);

        if(u16_ControlCount >= 32)// Sample after stabilization
        {
            u16_power_low = ary_DevicePowerLimit[(uint8_t)FactoryDevice_CondFan].u16_LowPowerLimit;
            u16_power_high = ary_DevicePowerLimit[(uint8_t)FactoryDevice_CondFan].u16_HighPowerLimit;
            if((u16_12V_power > u16_power_high) || (u16_12V_power < u16_power_low))
            {
                u8_FactoryDeviceFaultByte |= 0x10;
            }
        }
    }
    else if(u16_ControlCount < 58)
    {
        //Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 0);
        Test_FanCtrl(COOL_FAN, false);
        if(u16_ControlCount == 34)
        {
            Reset_DoubleDamper();
        }

        if(u16_ControlCount < 56)// Sample after stabilization
        {
            u16_power_low = ary_DevicePowerLimit[(uint8_t)FactoryDevice_Damper].u16_LowPowerLimit;
            u16_power_high = ary_DevicePowerLimit[(uint8_t)FactoryDevice_Damper].u16_HighPowerLimit;
            if((u16_12V_power > u16_power_high) || (u16_12V_power < u16_power_low))
            {
                u8_FactoryDeviceFaultByte |= 0x20;
            }
        }
    }
    else
    {
        if(false == wifi_match)
        {
            b_WiFiMatchError = true;
            u8_FactoryDeviceFaultByte |= 0x80;
        }
        
        Test_FanCtrl(REF_FAN, false);
        Test_FanCtrl(FRZ_FAN, false);
        Test_FanCtrl(COOL_FAN, false);
        //Reset_DoubleDamper();
        Drive_ValveReset();
        u16_ControlCount = 0;
        b_FctCtrlLoadOver = true;
        fct_room_temp = Get_SensorValue(SENSOR_ROOM);
    }
    u16_ControlCount++;
}

static void FactoryMode_ControlCooling(void)
{
    bool wifi_error = false;

    if(u16_ControlCount < (27 * 60))
    {
        if((u16_ControlCount % 240) < 120)
        {
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_On);
        }
        else
        {
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_Off);
        }
    }

    if(u16_ControlCount < (5 * 60))
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 30);
    }
    else if(u16_ControlCount < (5 * 60 + 1))
    {
        if(fct_room_temp > CON_32P0_DEGREE)
        {
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 30);
        }
        else
        {
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_Off);
        }
    }

    if(u16_ControlCount < (10 * 60))
    {
        if(u16_ControlCount < 20)
        {
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_FrzON_RefOFF);
        }
        else
        {
            Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, FREQ_108HZ);
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 69);
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 69);
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLOPEN);
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLCLOSE);
        }
    }
    else if(u16_ControlCount < (17 * 60))
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_FrzOFF_RefON);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, FREQ_108HZ);
        //Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 69);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 0);
        if(u16_ControlCount > (9 * 60))
        {
            b_WiFiStateUpdate = false;
        }

        if(b_WifiConnected && b_WiFiMatch)
        {
            b_WiFiMatchError = false;
        }

        if(b_WiFiStateUpdate || (b_WifiConnected && b_WiFiMatch))
        {
            if(false == b_FctWifiLedState)
            {
                b_FctWifiLedState = true;
            }
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 69);
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLOPEN);
        }
        else
        {
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 0);
            Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLCLOSE);
        }
    }
    else if(u16_ControlCount < (24 * 60))
    {
        if(u16_ControlCount == (17 * 60))
        {
            Set_FactoryEntryNumber(1);
            SetSysParam(SYSPARAM_INSPECTION, 1);
        }
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_FrzOFF_RefON);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, FREQ_108HZ);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 69);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 69);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLCLOSE);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLOPEN);
    }
    else
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, DS_DontCare);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, FREQ_108HZ);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefVarDamper, DS_DontCare);
        b_WiFiMatchError = false;
        if(b_WifiRebootResponsed == false)
        {
            if(b_WifiReboot == false)
            {
                if(execute_wifi_cmd_async(WIFI_CMD_REBOOT, wifi_reboot_response) == 0)
                {
                    b_WifiReboot = true;
                }
            }
        }

        if((u16_ControlCount > (25 * 60)) || (b_WifiRebootResponsed == true))
        {
            u8_FactoryDeviceFaultByte = 0;
            u8_FactoryDeviceFaultByte1 = 0;
            Stop_PollTimer();
            Update_RefSetTempBak(REF_LEVEL_5);	//ADD
            Update_FrzSetTempBak(FRZ_LEVEL_F18);	//ADD
            Update_RefVarSetTemp(eRefVar_Treasure);
            Set_UserMode(eFuzzy_Mode);
            Set_CoolingEntryMode(eMode_FactoryCompleted);
            FridgeState_Update(eFridge_Running);
        }
    }
    u16_ControlCount++;
}


bool Get_WifiConnectState(void)
{
    bool b_WiFiState;
    net_state_e net_state = get_dev_net_state();

    switch(net_state)
    {
        case ZM_APP_NET_STATE_NONE:
        case ZM_APP_NET_STATE_OFFLINE:
        case ZM_APP_NET_STATE_UAP:
        case ZM_APP_NET_STATE_UNPROV:
            b_WiFiState = false;
            break;
        case ZM_APP_NET_STATE_LOCAL:
        case ZM_APP_NET_STATE_UPDATING:
        case ZM_APP_NET_STATE_UPDATING_AUTO:
        case ZM_APP_NET_STATE_UPDATING_FORCE:
        case ZM_APP_NET_STATE_CLOUD:
        default:
            b_WiFiState = true;
            break;
    }

    return b_WiFiState;
}

static void Update_FactoryTotalPower(void)
{
    uint16_t u16_12V_power = Get_12VPower();
    uint16_t comp_power = Get_CompPower();
    uint16_t offset = U16_POWER_OFFSET;
    uint16_t voltage = 0;
    float voltage_coef = 0;
    uint16_t defrost_heater_power = 0;

    if(true == b_DefrostHeaterOn)
    {
        voltage = Get_CompBusVoltage();
        voltage_coef = ((float)voltage) / FLOAT_AC_VOLTAGE;
        voltage_coef *= voltage_coef;
        defrost_heater_power = (uint16_t)(voltage_coef * FLOAT_DEFROST_RATING);
    }

    if(Get_CompFeedbackFreq() > 0)
    {
        offset = U16_COMP_POWER_OFFSET;
    }
    // real power * 10
    u16_FactoryTotalPower = defrost_heater_power + u16_12V_power + offset + comp_power / 2;
}

static void Process_FactoryMode(void)
{
    uint8_t fct_sn[PRODUCT_SN_SIZE];

    Update_FactoryTotalPower();
    if(true == Get_SensorError(SENSOR_HUMIDITY))
    {
        u8_FactoryDeviceFaultByte |= 0x40;
    }

    if(b_FctWriteSnSuccess == true)
    { // sn写入成功
        if(false == b_FctCtrlLoadOver)
        {
            FactoryMode_ControlLoad();
        }
        else
        {
            FactoryMode_ControlCooling();
        }
    }
    else
    {
        b_FctWriteSnSuccess = true;
    }

    Check_Factory_Items();

    if(b_WiFiStateUpdate)
    {
        if(b_WiFiMatch == false)
        {
            b_WiFiMatch = is_wifi_arch_platform();
        }

        if(b_WifiFactoryResponsed == false)
        {
            if(b_WifiFactory == false)
            {
                if(execute_wifi_cmd_async(WIFI_CMD_FACTORY, wifi_factory_response) == 0)
                {
                    b_WifiFactory = true;
                }
            }
        }
        else if(b_WifiConnected == false)
        {
            b_WifiConnected = Get_WifiConnectState();
        }
        else if(b_WifiRebootResponsed == false) // 非智能商检
        {
            if(b_WifiReboot == false)
            {
                if(execute_wifi_cmd_async(WIFI_CMD_REBOOT, wifi_reboot_response) == 0)
                {
                    b_WifiReboot = true;
                }
            }
        }
    }

    if((true == b_WifiConnected) && (true == b_WiFiMatch))
    {
        if(false == b_FctWifiLedState)
        {
            b_FctWifiLedState = true;
        }
    }
}

static void Start_PollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_FactoryModeTimer,
        Process_FactoryMode,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

void FactoryMode_Init(void)
{
    fct_room_temp = Get_SensorValue(SENSOR_ROOM);
    b_FctCtrlLoadOver = false;
    u16_ControlCount = 0;
    b_WifiReboot = false;
    b_WiFiStateUpdate = true;
    b_WifiConnected = false;
    b_WiFiMatch = false;
    b_WiFiMatchError = false;
    b_WifiFactory = false;
    b_FctWriteSnSuccess = false;
    b_WifiRebootResponsed = false;
    b_WifiFactoryResponsed = false;
    Start_PollTimer(U16_FACTORY_CHECK_CYCLE_SECOND);
}

void FactoryMode_Exit(void)
{
    if(false == b_FctCtrlLoadOver)
    {
        Drive_ValveForce(false);
    }
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefVarDamper, DS_DontCare);
    Test_FanCtrl(REF_FAN, false);
    Test_FanCtrl(FRZ_FAN, false);
    Test_FanCtrl(COOL_FAN, false);
    Stop_PollTimer();
}

bool Get_FctWifiMatchError(void)
{
    return (b_WiFiMatchError);
}

bool Get_FctWifiLedState(void)
{
    return (b_FctWifiLedState);
}

void Get_FctUploadData(char *result, uint16_t len)
{
    str_n_cat(result, 1, factory_data);
}

uint16_t Get_FactoryRoomTemp(void)
{
    return (fct_room_temp);
}

bool Get_WifiFactoryModeResult(void)
{
    return b_WifiFactoryResponsed;
}

bool Get_WifiFactoryConnected(void)
{
    return b_WifiConnected;
}

bool Get_WifiFactoryMode(void)
{
    return b_WifiFactory;
}

uint8_t Get_FactoryDeviceFaultByte(void)
{
    return (u8_FactoryDeviceFaultByte);
}
uint16_t Get_FactoryTotalPower(void)
{
    return (u16_FactoryTotalPower);
}
