/*!
 * @file
 * @brief This file defines public constants, types and functions for the device parameter.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef PARAMETER_DEVICE_H
#define PARAMETER_DEVICE_H

#include <stdint.h>
#include "SimpleFsm.h"
#include "Driver_CompFrequency.h"
#include "Driver_AdSample.h"

#define U16_DOWN_LOAD_MAX_TIME_SECOND ((uint16_t)(420 * 60)) // 420min
#define U8_IMPROVE_FREQ_TIME_MINUTE ((uint8_t)120)
#define U8_FACTORY_COMPLETED_RUN_TIME ((uint16_t)30)

enum
{
    eCoolingCapacity_Normal = 0,
    eCoolingCapacity_FridgePowerOn,
    eCoolingCapacity_HighLoad,
    eCoolingCapacity_FastRunning,
    eCoolingCapacity_EnergyMode,
    eCoolingCapacity_FactoryCompleted
};
typedef uint8_t CoolingCapacityState_t;

enum
{
    eOutput_Normal = 0,
    eOutput_FridgePowerOn,
    eOutput_HighLoad,
    eOutput_FastRunning,
    eOutput_EnergyMode,
    eOutput_FactoryCompleted
};
typedef uint8_t CoolingOutputState_t;

uint8_t Get_CompFreqIndex(CoolingCapacityState_t state, uint16_t compOnMinute);
uint8_t Get_CompOffFanSettingIndex(RoomTempRange_t range);
uint8_t Get_CompOnFrzFanSettingIndex(uint8_t freqIndex);
uint8_t Get_CompOnFrzFanSettingIndex(uint8_t freqIndex);
uint8_t Get_CompOnRefFanSettingIndex(uint8_t freqIndex);
uint8_t Get_CondFanSettingIndex(uint8_t freqIndex);
CoolingOutputState_t Get_CoolingOutputState(void);
void Set_FrzFanAdjustParm(uint8_t adjust_value);
uint16_t Get_FrzFanAdjustParm(void);
void Set_CoolFanAdjustParm(uint8_t adjust_value);
uint16_t Get_CoolFanAdjustParm(void);
void Set_RefFanAdjustParm(uint8_t adjust_value);
uint16_t Get_RefFanAdjustParm(void);
uint8_t Get_CompOnPowerSaveFreq(void);
uint8_t Get_CompOnNoiseReduceFreq(void);

#endif
