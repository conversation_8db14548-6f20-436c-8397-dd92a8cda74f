/*!
 * @file
 * @brief This module covers the complete fan functionality.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "DisplayInterface.h"
#include "KeyManager.h"
#include "Parameter_TemperatureZone.h"
#include "Crc16_CCITT_FALSE.h"
#include "Core_CallBackTimer.h"
#include "Adpt_Flash.h"
#include "Driver_Flash.h"
#include "Adpt_Iwdg.h"
#include "Init_Mcu.h"
#include "Core_Types.h"
#include "syslog.h"
#include "SystemManager.h"
#include "user_config.h"


static ota_param_st *ota_zone = (ota_param_st *)BOOT_PARA_ADDRESS;
static ota_param_st ota_zone_cache;
static param_manager_st param_manager[PARAMTYPE_MAX];
static sys_param_st sysparam_latest;
static sys_param_st sysparam_history;
static sys_param_st sysparam_default = {
    .inspection = 0,
    .ref_temp = REF_LEVEL_5,
    .frz_temp = FRZ_LEVEL_F18,
    .infant_mode = eRefVar_Treasure,
    .user_mode = eFuzzy_Mode,
    .fridge_runtime = 0,
    .compressor_runtime = 0,
    .icemaker_func = 0,
    .partialfrz_func = 0,
};
static app_promote_st *app_promote = (app_promote_st *)APP_PROMOTE_ADDR;
static st_CoreCallbackTimer app_promote_timer;
static product_sn_st *product_sn = (product_sn_st *)FACTORY_ADDRESS;
static product_sn_manager_st product_sn_manager;


static uint32_t convert_from_bigendian32(uint32_t val)
{
    uint32_t temp = 0;
    uint8_t *p = (uint8_t *)&val;

    temp = (p[0] << 24) |
        (p[1] << 16) |
        (p[2] << 8) |
        p[3];
    return temp;
}

static uint16_t convert_from_bigendian16(uint16_t val)
{
    uint16_t temp = 0;
    uint8_t *p = (uint8_t *)&val;

    temp = (p[0] << 8) | p[1];
    return temp;
}

static void StartAppPromoteTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &app_promote_timer,
        ClearAppPromoteCount,
        tickSec,
        0,
        eCallbackTimer_Type_OneShot,
        eCallbackTimer_Priority_Normal);
}

static bool CheckAppPromoteCrc(void)
{
    uint16_t crc = Cal_CRC_MultipleData((uint8_t *)app_promote, offsetof(app_promote_st, crc16));
    if(app_promote->crc16 == crc)
    {
        return true;
    }
    return false;
}

static void UpdateAppPromoteCrc(void)
{
    app_promote->crc16 = Cal_CRC_MultipleData((uint8_t *)app_promote, offsetof(app_promote_st, crc16));
}


static void Init_AppPromote(void)
{
    if(app_promote->magic != OTA_MAGIC || CheckAppPromoteCrc() == false)
    {
        app_promote->magic = OTA_MAGIC;
        app_promote->count = 0;
        UpdateAppPromoteCrc();
    }
    StartAppPromoteTimer(APP_PROMOTE_TIMER);
}


static bool ParamValid(void *param, uint16_t size)
{
    uint16_t crc16;
    uint16_t pcrc;

    memcpy(&pcrc, param + size, sizeof(uint16_t));
    crc16 = Cal_CRC_MultipleData(param, size);
    if(crc16 == pcrc)
    {
        return true;
    }
    return false;
}

static bool ParamWriteable(void *param, uint16_t len)
{
    uint8_t buf[4];
    uint8_t *temp;

    temp = (uint8_t *)param;
    memset(buf, FLASH_ERASE_STATE, sizeof(buf));

    while(len)
    {
        if(memcmp(temp, buf, MIN(len, 4)))
        {
            return false;
        }
        len -= MIN(len, 4);
    }
    return true;
}

static void *GetParamBlockUpdate(param_manager_st *pm)
{
    void *param;
    uint16_t psize;
    uint32_t offset = 0;

    psize = pm->param_size + PARM_CRC_LENGTH;
    offset += sizeof(param_header_st);
    param = (void *)(pm->start_addr + pm->blockno * FLASH_SECTOR_SIZE + sizeof(param_header_st));

    while(offset + psize <= FLASH_SECTOR_SIZE)
    {
        if(ParamWriteable(param, psize))
        {
            return param;
        }
        param += psize;
        offset += psize;
    }
    return NULL;
}

static void FormatParamBlock(param_manager_st *pm, uint16_t block)
{
    uint32_t addr;
    param_header_st hdr;

    hdr.magic = pm->magic;
    hdr.version = pm->version;
    hdr.expired = PARM_NOTEXPIRED;
    addr = pm->start_addr + block * FLASH_SECTOR_SIZE;
    FlashSectorErase(addr);
    FlashWriteBytes(addr, (uint8_t *)&hdr, sizeof(param_header_st));
}

static void ParamBlockExpire(param_manager_st *pm)
{
    uint16_t crc;
    uint32_t val;
    uint32_t addr;
    uint16_t hblock;
    uint32_t *expired;

    hblock = pm->blockno;
    addr = pm->start_addr + pm->blockno * FLASH_SECTOR_SIZE + offsetof(param_header_st, expired);
    expired = (uint32_t *)addr;
    if(*expired == PARM_NOTEXPIRED)
    {
        val = PARM_HISTORY;
        FlashWriteBytes(addr, (uint8_t *)&val, sizeof(uint32_t));
    }
    pm->blockno = (pm->blockno + 1) % pm->blocks;
    FormatParamBlock(pm, pm->blockno);
    pm->update = GetParamBlockUpdate(pm);
    if(pm->update)
    {
        crc = Cal_CRC_MultipleData(pm->latest, pm->param_size);
        FlashWriteBytes((uint32_t)pm->update, pm->latest, pm->param_size);
        pm->update += pm->param_size;
        FlashWriteBytes((uint32_t)pm->update, (uint8_t *)&crc, sizeof(crc));
        pm->update = GetParamBlockUpdate(pm);
    }

    if(hblock != pm->blockno)
    {
        val = PARM_EXPIRED;
        FlashWriteBytes(addr, (uint8_t *)&val, sizeof(uint32_t));
    }
}

static void ParamStartup(param_manager_st *pm)
{
    void *param;
    uint16_t psize;
    uint16_t block;
    uint8_t found = 0;
    uint32_t offset = 0;
    param_header_st *header;

    for(block = 0; block < pm->blocks; block++)
    {
        header = (param_header_st *)(pm->start_addr + block * FLASH_SECTOR_SIZE);
        if(header->magic == pm->magic && header->version == pm->version &&
            header->expired == PARM_HISTORY)
        {
            found = 1;
            pm->blockno = block;
            break;
        }
    }

    if(!found)
    {
        for(block = 0; block < pm->blocks; block++)
        {
            header = (param_header_st *)(pm->start_addr + block * FLASH_SECTOR_SIZE);
            if(header->magic == pm->magic && header->version == pm->version &&
                header->expired == PARM_NOTEXPIRED)
            {
                found = 1;
                pm->blockno = block;
                break;
            }
        }

        if(!found)
        {
            FormatParamBlock(pm, 0);
        }
    }
    param = (void *)(pm->start_addr + pm->blockno * FLASH_SECTOR_SIZE + sizeof(param_header_st));
    offset += sizeof(param_header_st);
    psize = pm->param_size + PARM_CRC_LENGTH;
    found = 0;
    while(offset + psize <= FLASH_SECTOR_SIZE)
    {
        if(ParamWriteable(param, psize))
        {
            if(offset > sizeof(param_header_st) && ParamValid(param - psize, pm->param_size))
            {
                memcpy(pm->latest, param - psize, pm->param_size);
            }
            else
            {
                memcpy(pm->latest, pm->defparam, pm->param_size);
                pm->is_default = true;
            }
            pm->update = param;
            found = 1;
            break;
        }

        if(ParamValid(param, pm->param_size))
        {
            memcpy(pm->history, param, pm->param_size);
            pm->has_history = true;
        }
        param += psize;
        offset += psize;
    }

    if(!found)
    {
        if(ParamValid(param - psize, pm->param_size))
        {
            memcpy(pm->latest, param - psize, pm->param_size);
        }
        else
        {
            memcpy(pm->latest, pm->defparam, pm->param_size);
            pm->is_default = true;
        }
        ParamBlockExpire(pm);
    }
}

static void Init_SysParam(void)
{
    param_manager[PARAMTYPE_SYSPARAM].blockno = 0;
    param_manager[PARAMTYPE_SYSPARAM].blocks = PARAM_SIZE / FLASH_SECTOR_SIZE;
    param_manager[PARAMTYPE_SYSPARAM].start_addr = PARAM_ADDRESS;
    param_manager[PARAMTYPE_SYSPARAM].latest = &sysparam_latest;
    param_manager[PARAMTYPE_SYSPARAM].history = &sysparam_history;
    param_manager[PARAMTYPE_SYSPARAM].defparam = &sysparam_default;
    param_manager[PARAMTYPE_SYSPARAM].param_size = sizeof(sys_param_st);
    param_manager[PARAMTYPE_SYSPARAM].magic = SYSPARM_MAGIC;
    param_manager[PARAMTYPE_SYSPARAM].version = SYSPARM_VERSION;
    param_manager[PARAMTYPE_SYSPARAM].cycles_100ms = SYSPARM_CYCLES;
    param_manager[PARAMTYPE_SYSPARAM].count_100ms = 0;

    ParamStartup(&param_manager[PARAMTYPE_SYSPARAM]);
    if(param_manager[PARAMTYPE_SYSPARAM].is_default &&
        param_manager[PARAMTYPE_SYSPARAM].has_history)
    {
        sysparam_latest.fridge_runtime = sysparam_history.fridge_runtime;
        sysparam_latest.compressor_runtime = sysparam_history.compressor_runtime;
        sysparam_latest.inspection = sysparam_history.inspection;
    }
    Set_FactoryEntryNumber(sysparam_latest.inspection);
    Update_RefSetTemp(sysparam_latest.ref_temp);
    Update_RefSetTempBak(sysparam_latest.ref_temp);
    Update_FrzSetTemp(sysparam_latest.frz_temp);
    Update_FrzSetTempBak(sysparam_latest.frz_temp);
    Update_RefVarSetTemp(sysparam_latest.infant_mode);
    Set_UserMode(sysparam_latest.user_mode);
}

static void SaveParam(param_manager_st *pm)
{
    uint16_t crc;
    uint32_t val;
    uint32_t addr;

    if(NULL == pm->update)
    {
        ParamBlockExpire(pm);
        return;
    }
    crc = Cal_CRC_MultipleData(pm->latest, pm->param_size);
    FlashWriteBytes((uint32_t)pm->update, pm->latest, pm->param_size);
    pm->update += pm->param_size;
    FlashWriteBytes((uint32_t)pm->update, (uint8_t *)&crc, sizeof(crc));
    val = PARM_HISTORY;
    addr = pm->start_addr + pm->blockno * FLASH_SECTOR_SIZE + offsetof(param_header_st, expired);
    FlashWriteBytes(addr, (uint8_t *)&val, sizeof(uint32_t));
    pm->update = GetParamBlockUpdate(pm);
}

static void RecoveryParam(param_type_e pt)
{
    void *update;
    param_manager_st *pm;

    if(pt >= PARAMTYPE_MAX || pt < 0)
    {
        err("param type (%d) is invaild\n", pt);
        return;
    }

    pm = &param_manager[pt];
    pm->dirty = 0;
    pm->count_100ms = 0;
    memcpy(pm->history, pm->latest, pm->param_size);
    memcpy(pm->latest, pm->defparam, pm->param_size);
}

static bool CheckProductSnCrc(void)
{
    uint16_t crc = Cal_CRC_MultipleData((uint8_t *)(&product_sn_manager.sn), offsetof(product_sn_st, crc16));
    if(product_sn_manager.sn.crc16 == crc)
    {
        return true;
    }
    return false;
}

static void UpdateProductModel(void)
{
    uint8_t size;
    uint8_t index;
    char ch;

    size = strlen(PRODUCT_MODEL_PREFIX);
    if(CheckSnFormat(product_sn_manager.sn.data, PRODUCT_SN_SIZE) == false)
    {
        return;
    }

    memcpy(product_sn_manager.user_model, PRODUCT_MODEL_PREFIX, size);
    for( index = 0; index < PRODUCT_MODEL_BYTES; index++)
    {
        ch = product_sn_manager.sn.data[PRODUCT_MODEL_OFFSET + index];
        if(ch >= 65 && ch <= 90)
        {
            product_sn_manager.user_model[size + index] = ch + 32;
            product_sn_manager.model[index] = ch + 32;
        }
        else
        {
            product_sn_manager.user_model[size + index] = ch;
            product_sn_manager.model[index] = ch;
        }
    }
    product_sn_manager.exist = true;
}

static void Init_ProductSn(void)
{
    memset(&product_sn_manager, 0, sizeof(product_sn_manager_st));
    memcpy(&product_sn_manager.sn, product_sn, sizeof(product_sn_st));
    product_sn_manager.exist = false;
    if(product_sn_manager.sn.magic == PRODUCT_SN_MAGIC && true == CheckProductSnCrc())
    {
        UpdateProductModel();
    }
}

void Init_Flash(void)
{
    memcpy(&ota_zone_cache, ota_zone, sizeof(ota_param_st));
    Init_SysParam();
    Init_AppPromote();
	Init_ProductSn();
}

uint32_t GetMcuVersion(void)
{
    if(ota_zone_cache.ota_magic != OTA_MAGIC ||
        ota_zone_cache.ota_flag != OTA_DONE_FLAG ||
        ota_zone_cache.ota_version > MCU_MAX_VERSION)
    {
        return (uint32_t)OTA_FACTORY_VERSION;
    }
    else
    {
        return ota_zone_cache.ota_version;
    }
}

void SetOtaFlag(bool quite)
{
    en_result_t res;
    uint32_t version;

    version = GetMcuVersion();
    debug("set ota flag start!");
    ota_zone_cache.ota_magic = OTA_MAGIC;
    ota_zone_cache.ota_flag = OTA_START_FLAG;
    ota_zone_cache.quite_flag = OTA_NORMAL_FLAG;
    if(quite)
    {
        ota_zone_cache.quite_flag = OTA_QUITE_FLAG;
    }
    ota_zone_cache.ota_version = version;
    res = FlashSectorErase(BOOT_PARA_ADDRESS);
    debug("FlashSectorErase %lx, %d\n", BOOT_PARA_ADDRESS, res);
    res = FlashWriteBytes(BOOT_PARA_ADDRESS, (uint8_t *)&ota_zone_cache, sizeof(ota_zone_cache));
    debug("FlashWriteBytes %lx, %d\n", BOOT_PARA_ADDRESS, res);
    debug("set ota flag success!\n");
    return;
}

void SetTestUartOtaFlag(void)
{
    en_result_t res;
    uint32_t version;

    version = GetMcuVersion();
    debug("set ota flag start!");
    ota_zone_cache.ota_magic = OTA_MAGIC;
    ota_zone_cache.ota_flag = OTA_TESTUART_FLAG;
    ota_zone_cache.quite_flag = OTA_NORMAL_FLAG;
    ota_zone_cache.ota_version = version;
    res = FlashSectorErase(BOOT_PARA_ADDRESS);
    debug("FlashSectorErase %lx, %d\n", BOOT_PARA_ADDRESS, res);
    res = FlashWriteBytes(BOOT_PARA_ADDRESS, (uint8_t *)&ota_zone_cache, sizeof(ota_zone_cache));
    debug("FlashWriteBytes %lx, %d\n", BOOT_PARA_ADDRESS, res);
    debug("set ota flag success!\n");
    return;
}

bool IsQuiteOtaBoot(void)
{
    uint32_t val;
    uint32_t addr;

    if(ota_zone_cache.ota_magic == OTA_MAGIC &&
        ota_zone_cache.ota_flag == OTA_DONE_FLAG &&
        ota_zone_cache.quite_flag == OTA_QUITE_FLAG)
    {
        if(ota_zone->quite_flag == OTA_QUITE_FLAG)
        {
            val = OTA_NORMAL_FLAG;
            addr = BOOT_PARA_ADDRESS + offsetof(ota_param_st, quite_flag);
            FlashWriteBytes(addr, (uint8_t *)&val, sizeof(uint32_t));
        }
        return true;
    }
    return false;
}

void ClearOtaParam(void)
{
    FlashSectorErase(BOOT_PARA_ADDRESS);
}

void UpdateSysParam(void)
{
    void *update;
    param_manager_st *pm;
    param_type_e pt = PARAMTYPE_SYSPARAM;

    for(; pt < PARAMTYPE_MAX; pt++)
    {
        pm = &param_manager[pt];
        if(pm->cycles_100ms > 0 &&
            ++pm->count_100ms >= pm->cycles_100ms)
        {
            pm->count_100ms = 0;
            if(pm->dirty)
            {
                pm->dirty = 0;
                SaveParam(pm);
            }
        }
    }
    return;
}

void RecoverySysParam(void)
{
    RecoveryParam(PARAMTYPE_SYSPARAM);
    //sysparam_latest.fridge_runtime = sysparam_history.fridge_runtime;
    //sysparam_latest.compressor_runtime = sysparam_history.compressor_runtime;
    //sysparam_latest.inspection = sysparam_history.inspection;
    SaveParam(&param_manager[PARAMTYPE_SYSPARAM]);
    Set_FactoryEntryNumber(sysparam_latest.inspection);
    Update_RefSetTemp(sysparam_latest.ref_temp);
    Update_FrzSetTemp(sysparam_latest.frz_temp);
    Update_RefSetTempBak(sysparam_latest.ref_temp);
    Update_FrzSetTempBak(sysparam_latest.frz_temp);
    Update_RefVarSetTemp(sysparam_latest.infant_mode);
    Set_UserMode(sysparam_latest.user_mode);
    Set_LockStatus(true);
}

int GetSysParam(sysparam_type_e type, uint8_t *val)
{
    switch(type)
    {
        case SYSPARAM_INSPECTION:
            *val = sysparam_latest.inspection;
            break;
        case SYSPARAM_REFTEMP:
            *val = sysparam_latest.ref_temp;
            break;
        case SYSPARAM_FRZTEMP:
            *val = sysparam_latest.frz_temp;
            break;
        case SYSPARAM_INFANT_MODE:
            *val = sysparam_latest.infant_mode;
            break;
        case SYSPARAM_USER_MODE:
            *val = sysparam_latest.user_mode;
            break;
        case SYSPARAM_FRIDGE_RUNTIME:
            *val = sysparam_latest.fridge_runtime;
            break;
        case SYSPARAM_COMPRESSOR_RUNTIME:
            *val = sysparam_latest.compressor_runtime;
            break;
        case SYSPARAM_ICEMAKER_FUNC:
            *val = sysparam_latest.icemaker_func;
            break;
        case SYSPARAM_PARTIALFRZ_FUNC:
            *val = sysparam_latest.partialfrz_func;
            break;
        default:
            return -1;
    }
    return 0;
}

int SetSysParam(sysparam_type_e type, uint8_t val)
{
    switch(type)
    {
        case SYSPARAM_INSPECTION:
            if(sysparam_latest.inspection != val)
            {
                sysparam_latest.inspection = val;
                SaveParam(&param_manager[PARAMTYPE_SYSPARAM]);
                param_manager[PARAMTYPE_SYSPARAM].dirty = 0;
            }
            break;
        case SYSPARAM_REFTEMP:
            if(sysparam_latest.ref_temp != val)
            {
                sysparam_latest.ref_temp = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_FRZTEMP:
            if(sysparam_latest.frz_temp != val)
            {
                sysparam_latest.frz_temp = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_INFANT_MODE:
            if(sysparam_latest.infant_mode != val)
            {
                sysparam_latest.infant_mode = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_USER_MODE:
            if(sysparam_latest.user_mode != val)
            {
                sysparam_latest.user_mode = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_FRIDGE_RUNTIME:
            if(sysparam_latest.fridge_runtime != val)
            {
                sysparam_latest.fridge_runtime = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_COMPRESSOR_RUNTIME:
            if(sysparam_latest.compressor_runtime != val)
            {
                sysparam_latest.compressor_runtime = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_ICEMAKER_FUNC:
            if(sysparam_latest.icemaker_func != val)
            {
                sysparam_latest.icemaker_func = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        case SYSPARAM_PARTIALFRZ_FUNC:
            if(sysparam_latest.partialfrz_func != val)
            {
                sysparam_latest.partialfrz_func = val;
                param_manager[PARAMTYPE_SYSPARAM].dirty = 1;
            }
            break;
        default:
            return -1;
    }

    return 0;
}

int GetSysParamFromFlash(sysparam_type_e type, uint8_t *val)
{
    void *param;
    uint16_t psize;
    uint16_t block;
    uint8_t found = 0;
    uint32_t offset = 0;
    sys_param_st *sp = NULL;
    param_header_st *header;
    param_manager_st *pm = &param_manager[PARAMTYPE_SYSPARAM];

    param = (void *)(pm->start_addr + pm->blockno * FLASH_SECTOR_SIZE + sizeof(param_header_st));
    offset += sizeof(param_header_st);
    psize = pm->param_size + PARM_CRC_LENGTH;
    found = 0;
    while(offset + psize <= FLASH_SECTOR_SIZE)
    {
        if(ParamWriteable(param, psize))
        {
            break;
        }
        sp = param;
        param += psize;
        offset += psize;
    }

    if(sp == NULL || ParamValid(sp, pm->param_size) == false)
    {
        return -1;
    }

    switch(type)
    {
        case SYSPARAM_INSPECTION:
            *val = sp->inspection;
            break;
        case SYSPARAM_REFTEMP:
            *val = sp->ref_temp;
            break;
        case SYSPARAM_FRZTEMP:
            *val = sp->frz_temp;
            break;
        case SYSPARAM_INFANT_MODE:
            *val = sp->infant_mode;
            break;
        case SYSPARAM_USER_MODE:
            *val = sp->user_mode;
            break;
        case SYSPARAM_FRIDGE_RUNTIME:
            *val = sp->fridge_runtime;
            break;
        case SYSPARAM_COMPRESSOR_RUNTIME:
            *val = sp->compressor_runtime;
            break;
        case SYSPARAM_ICEMAKER_FUNC:
            *val = sp->icemaker_func;
            break;
        case SYSPARAM_PARTIALFRZ_FUNC:
            *val = sp->partialfrz_func;
            break;
        default:
            return -1;
    }
    return 0;
}

uint32_t GetAppPromoteCount(void)
{
    return app_promote->count;
}

void ClearAppPromoteCount(void)
{
    info("app is promote\n");
    app_promote->count = 0;
	UpdateAppPromoteCrc();
}

uint32_t GetBootVersion(void)
{
    return app_promote->boot_version;
}

uint32_t GetBootCrc(void)
{
    return app_promote->boot_crc;
}

uint32_t GetAppVersion(void)
{
    app_imageheader_st header;
    unsigned char *buf;
    uint32_t len;

    memcpy(&header, (void *)(APP_ADDRESS - APP_HEADER_SIZE), sizeof(app_imageheader_st));
    header.magic = convert_from_bigendian32(header.magic);
    header.version = convert_from_bigendian32(header.version);
    if(header.magic == APP_MAGIC_NUM)
    {
        return header.version;
    }
    return 0;
}

uint32_t GetAppCrc(void)
{
    return app_promote->app_crc;
}

void SaveOfflineLog(void *buf, uint32_t len)
{
    uint32_t addr = PANIC_LOG_ADDRESS;
    uint32_t offset = 0;

    for(; offset < PANIC_LOG_SIZE; offset += FLASH_SECTOR_SIZE)
    {
        FlashSectorErase(addr + offset);
    }

    len = len > PANIC_LOG_SIZE ? PANIC_LOG_SIZE : len;
    offset = 0;
    while(len > 0)
    {
        if(len >= FLASH_SECTOR_SIZE)
        {
            FlashWriteBytes(addr + offset, buf + offset, FLASH_SECTOR_SIZE);
            len -= FLASH_SECTOR_SIZE;
            offset += FLASH_SECTOR_SIZE;
            continue;
        }
        FlashWriteBytes(addr + offset, buf + offset, len);
        len = 0;
    }
}

int8_t WriteProductSn(uint8_t *sn, uint8_t size, bool sync)
{
    en_result_t ret;
    uint8_t retry = 3;
    if(size < PRODUCT_SN_SIZE || sn[0] == 0)
    {
        return -1;
    }

    if(CheckSnFormat(sn, PRODUCT_SN_SIZE) == false)
    {
        return -1;
    }

    product_sn_manager.exist = false;
    product_sn_manager.sn.magic = PRODUCT_SN_MAGIC;
    memcpy(product_sn_manager.sn.data, sn, PRODUCT_SN_SIZE);
    UpdateProductModel();
    product_sn_manager.sn.crc16 = Cal_CRC_MultipleData((uint8_t *)(&product_sn_manager.sn), offsetof(product_sn_st, crc16));
    if(sync)
    {
        do
        {
            FlashSectorErase(FACTORY_ADDRESS);
            ret = FlashWriteBytes(FACTORY_ADDRESS, (uint8_t *)(&product_sn_manager.sn), sizeof(product_sn_st));
        }while(retry-- > 0 && ret != 0);

        if(ret != 0)
        {
            return -1;
        }
    }
    return 0;
}

int8_t ReadProductSn(uint8_t *sn, uint8_t size)
{
    if(size < PRODUCT_SN_SIZE || product_sn_manager.exist == false)
    {
        return -1;
    }

    memcpy(sn, product_sn_manager.sn.data, PRODUCT_SN_SIZE);
    return 0;
}

int8_t ReadProductUserModel(uint8_t *user_model, uint8_t size)
{
    if(product_sn_manager.exist == false || size < PRODUCT_MODEL_SIZE)
    {
        return -1;
    }

    memcpy(user_model, product_sn_manager.user_model, PRODUCT_MODEL_SIZE);
    return 0;
}

int8_t ReadProductModel(uint8_t *model, uint8_t size)
{
    if(product_sn_manager.exist == false || size < PRODUCT_MODEL_BYTES)
    {
        return -1;
    }

    memcpy(model, product_sn_manager.model, PRODUCT_MODEL_BYTES);
    return 0;
}

int8_t ReadProductModel_508Pro(uint8_t *model, uint8_t size)
{
    uint8_t * user_model = (uint8_t *)USER_MODEL;
    if(size < PRODUCT_MODEL_BYTES)
    {
        return -1;
    }
    user_model += strlen(PRODUCT_MODEL_PREFIX);
    strncpy((char *)model, (const char *)user_model, PRODUCT_MODEL_BYTES);
    return 0;
}


void ClearProductSn(void)
{
    FlashSectorErase(FACTORY_ADDRESS);
    memset(&product_sn_manager, 0, sizeof(product_sn_manager_st));
}

bool CheckSnFormat(uint8_t *sn, uint8_t size)
{
    if(size < PRODUCT_SN_SIZE)
    {
        return false;
    }

    if(sn[PRODUCT_MODEL_OFFSET - 1] != '/')
    {
        return false;
    }

    if(sn[PRODUCT_YEAR_OFFSET] < 48 || sn[PRODUCT_YEAR_OFFSET] > 57)
    {
        return false;
    }

    if((sn[PRODUCT_MONTH_OFFSET] <= 48 || sn[PRODUCT_YEAR_OFFSET] > 57) &&
       (sn[PRODUCT_MONTH_OFFSET] < 65 || sn[PRODUCT_YEAR_OFFSET] > 67))
    {
        return false;
    }

    if((sn[PRODUCT_DAY_OFFSET] <= 48 || sn[PRODUCT_DAY_OFFSET] > 57) &&
       (sn[PRODUCT_DAY_OFFSET] < 65 || sn[PRODUCT_DAY_OFFSET] > 90))
    {
        return false;
    }

    if((sn[PRODUCT_SEQ_THOUSAND_OFFSET] < 48 || sn[PRODUCT_SEQ_THOUSAND_OFFSET] > 57) &&
       (sn[PRODUCT_SEQ_THOUSAND_OFFSET] < 65 || sn[PRODUCT_SEQ_THOUSAND_OFFSET] > 90))
    {
        return false;
    }


    if(sn[PRODUCT_SEQ_HUNDRED_OFFSET] < 48 || sn[PRODUCT_SEQ_HUNDRED_OFFSET] > 57)
    {
        return false;
    }

    
    if(sn[PRODUCT_SEQ_TEN_OFFSET] < 48 || sn[PRODUCT_SEQ_TEN_OFFSET] > 57)
    {
        return false;
    }
    
    if(sn[PRODUCT_SEQ_ONE_OFFSET] < 48 || sn[PRODUCT_SEQ_ONE_OFFSET] > 57)
    {
        return false;
    }

    return true;
}

