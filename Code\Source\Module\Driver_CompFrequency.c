/*!
 * @file
 * @brief This module is Compressor Driver.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include "Adpt_PWM.h"

#include "Core_Types.h"
#include "Core_TimeBase.h"
#include "Core_TimerLibrary.h"
#include "Core_CallBackTimer.h"
#include "Driver_CompFrequency.h"
#include "Parameter_Device.h"
#include "CoolingCycle.h"
#include "syslog.h"


static st_CoreCallbackTimer st_DriveCompFreqTimerOut;
static DriveCompFreq_st st_DriveCompFreq;

static const uint16_t ARY_Comp_RunFreq[][FREQ_MAX] = {
    { 0, 41, 45, 54, 55, 60, 45, 70, 70, 70, 80, 80 }, //	0s - 2min
    { 0, 41, 45, 54, 55, 60, 70, 80, 80, 80, 108, 108 }, // 2min - 4min
    { 0, 41, 45, 54, 55, 60, 70, 80, 108, 118, 136, 147 } // 4min -
};

static void Driver_CompFreqStart(void)
{
}

static void Driver_CompFreqStop(void)
{
    st_DriveCompFreq.b_WakeupFlag = false;
    st_DriveCompFreq.u8_OnTimeSecs = 0;
    st_DriveCompFreq.u16_CompFreq = 0;
    st_DriveCompFreq.u8_CompFreqIndex = 0;
}

static void Driver_CompFreqUpdateFreq(void)
{
    uint8_t u8_Index = 0;
    //CoolingCapacityState_t state = Get_CoolingCapacityState();
    //FridgeState_t fridge_state = Get_FridgeState();

    if(0 == st_DriveCompFreq.u8_CompFreqIndex)
    {
        st_DriveCompFreq.u16_CompFreq = 0;
    }
    else if(0 != st_DriveCompFreq.u16_CompFreqAdjust)
    {
        st_DriveCompFreq.u16_CompFreq = 
            st_DriveCompFreq.u16_CompFreqAdjust == MAX_UINT8 ? 0 : st_DriveCompFreq.u16_CompFreqAdjust;
    }
    else
    {
        if(st_DriveCompFreq.u8_OnTimeSecs < U8_TIME2MINUTE)
        {
            u8_Index = 0;
        }
        else if(st_DriveCompFreq.u8_OnTimeSecs < U8_TIME4MINUTE)
        {
            u8_Index = 1;
        }
        else
        {
            u8_Index = 2;
        }
        
        //�̼��ӳ���
        //if((CoolingCapacityState_t)eCoolingCapacity_FactoryCompleted == state)
        //�̼첻��Ƶ
        /*if(((FridgeState_t)eFridge_Factory) == fridge_state)
        {
            u8_Index = 2;
        }*/

        st_DriveCompFreq.u16_CompFreq = ARY_Comp_RunFreq[u8_Index][st_DriveCompFreq.u8_CompFreqIndex];
    }
}

static void Driver_CompFreqUpdateTimeOut(void)
{
    if(U8_TIME4MINUTE > st_DriveCompFreq.u8_OnTimeSecs)
    {
        st_DriveCompFreq.u8_OnTimeSecs = st_DriveCompFreq.u8_OnTimeSecs +
            U8_COMP_FREQUENCY_UPDATEINTERVAL_SECS;
    }

    Driver_CompFreqUpdateFreq();
}

void Driver_CompFreqInit(void)
{
    st_DriveCompFreq.b_Init = true;
    st_DriveCompFreq.b_ForceOff = false;
    st_DriveCompFreq.b_WakeupFlag = false;
    st_DriveCompFreq.u8_OnTimeSecs = 0;
    st_DriveCompFreq.u16_CompFreq = 0;
    st_DriveCompFreq.u8_CompFreqIndex = 0;
    st_DriveCompFreq.u16_CompFreqAdjust = 0;
}

void Driver_CompFreqForce(bool b_State)
{
    st_DriveCompFreq.b_ForceOff = b_State;
    if(true == st_DriveCompFreq.b_WakeupFlag)
    {
        Driver_CompFreqStop();
    }
}

void Driver_CompFreqSet(uint8_t u8_CompFreqIndex)
{
    if(u8_CompFreqIndex >= FREQ_MAX)
    {
        u8_CompFreqIndex = 0;
    }

    st_DriveCompFreq.u8_CompFreqIndex = u8_CompFreqIndex;

    if(false == st_DriveCompFreq.b_ForceOff)
    {
        if(0 == st_DriveCompFreq.u8_CompFreqIndex)
        {
            if(true == st_DriveCompFreq.b_WakeupFlag)
            {
                Driver_CompFreqStop();
            }
        }
        else
        {
            if(false == st_DriveCompFreq.b_WakeupFlag)
            {
                st_DriveCompFreq.b_WakeupFlag = true;
                st_DriveCompFreq.u8_OnTimeSecs = 0;
                Core_CallbackTimer_TimerStart(&st_DriveCompFreqTimerOut,
                    Driver_CompFreqUpdateTimeOut,
                    U8_COMP_FREQUENCY_UPDATEINTERVAL_SECS,
                    0,
                    eCallbackTimer_Type_Periodic,
                    eCallbackTimer_Priority_Normal);
                Driver_CompFreqUpdateFreq();
                Driver_CompFreqStart();
            }
        }
    }
}

void Driver_CompAdjustFreqSet(uint16_t u16_CompFreq)
{
    uint16_t u16_freq = 0;
    if(u16_CompFreq < U16_COMP_FREQINDEX_MIN)
    {
        u16_freq = U16_COMP_FREQINDEX_MIN;
    }
    else if(u16_CompFreq > U16_COMP_FREQINDEX_MAX)
    {
        u16_freq = U16_COMP_FREQINDEX_MAX;
    }
    else
    {
        u16_freq = u16_CompFreq;
    }

    st_DriveCompFreq.u16_CompFreqAdjust = u16_freq;
}

uint16_t Get_CompFreq(void)
{
    return (st_DriveCompFreq.u16_CompFreq);
}

void Set_CompFreqMicroAdjustParm(uint16_t comp_freq_adjust_value)
{
    st_DriveCompFreq.u16_CompFreqAdjust = comp_freq_adjust_value;
}

uint16_t Get_CompFreqMicroAdjustParm(void)
{
    return (st_DriveCompFreq.u16_CompFreqAdjust);
}
