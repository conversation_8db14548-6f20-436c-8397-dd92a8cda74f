/*!
 * @file
 * @brief This module covers the complete fan functionality.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include "Driver_DoorSwitch.h"
#include "SystemTimerModule.h"
#include "Driver_Emulator.h"

#if CONIG_EMULATOR_ENABLE

fridge_emmulator_st emmulator = 
{
    .sensor_ref = CON_20P0_DEGREE,
    .sensor_ref_error = false,
    .sensor_ref_defrost = CON_20P0_DEGREE,
    .sensor_ref_defrost_error = false,
    .sensor_vv = CON_20P0_DEGREE,
    .sensor_vv_error = false,
    .sensor_ac = 0,
    .sensor_ac_error = false,
    .sensor_dc = 0,
    .sensor_dc_error = false,
    .sensor_humidity = HUMIDITY_AD_PCT25,
    .sensor_humidity_error = false,
    .sensor_room = CON_20P0_DEGREE,
    .sensor_room_error = false,
    .sensor_frz = CON_20P0_DEGREE,
    .sensor_frz_error = false,
    .sensor_defrost = CON_20P0_DEGREE,
    .sensor_defrost_error = false,
    .rt = RT_BELOW35,
    .rh = HBELOW25,
    .fast_forward = 0,
};

RoomTempRange_t Get_RoomTempRange(void)
{
    return emmulator.rt;
}

HumidityRange_t Get_HumidityRange(void)
{
    return emmulator.rh;
}

uint16_t Get_SensorValue(SensorType_t type)
{
    switch(type)
    {
    case SENSOR_REF:
        return emmulator.sensor_ref;
    case SENSOR_REF_DEFROST:
        return emmulator.sensor_ref_defrost;
    case SENSOR_VV:
        return emmulator.sensor_vv;
    case SENSOR_AC:
        return emmulator.sensor_ac;
    case SENSOR_DC:
        return emmulator.sensor_dc;
    case SENSOR_HUMIDITY:
        return emmulator.sensor_humidity;
    case SENSOR_ROOM:
        return emmulator.sensor_room;
    case SENSOR_FRZ:
        return emmulator.sensor_frz;
    case SENSOR_DEFROST:
        return emmulator.sensor_defrost;
    default:
        return 0;
    }
}

bool Get_SensorError(SensorType_t type)
{
    switch(type)
    {
    case SENSOR_REF:
        return emmulator.sensor_ref_error;
    case SENSOR_REF_DEFROST:
        return emmulator.sensor_ref_defrost_error;
    case SENSOR_VV:
        return emmulator.sensor_vv_error;
    case SENSOR_AC:
        return emmulator.sensor_ac_error;
    case SENSOR_DC:
        return emmulator.sensor_dc_error;
    case SENSOR_HUMIDITY:
        return emmulator.sensor_humidity_error;
    case SENSOR_ROOM:
        return emmulator.sensor_room_error;
    case SENSOR_FRZ:
        return emmulator.sensor_frz_error;
    case SENSOR_DEFROST:
        return emmulator.sensor_defrost_error;
    default:
        return false;
    }
}

uint8_t emmulator_ref_left_door_in()
{
    return emmulator.ref_left_door_in;
}

uint8_t      emmulator_ref_right_door_in()
{
    return emmulator.ref_right_door_in;
}

uint8_t emmulator_frz_left_door_in()
{
    return emmulator.frz_left_door_in;
}

uint8_t      emmulator_frz_right_door_in()
{
    return emmulator.frz_right_door_in;
}

uint16_t Get_DoorOpenTimeSecond(DoorTypeId_t typeId)
{
    switch(typeId)
    {
    case DOOR_REF_LEFT:
        return emmulator.ref_left_door_open_time;
    case DOOR_REF_RIGHT:
        return emmulator.ref_right_door_open_time;
    case DOOR_FRZ_LEFT:
        return emmulator.frz_left_door_open_time;
    case DOOR_FRZ_RIGHT:
        return emmulator.frz_right_door_open_time;
    case DOOR_REF:
        return emmulator.ref_door_open_time;
    case DOOR_FRZ:
        return emmulator.frz_door_open_time;
    case DOOR_ALL:
        return emmulator.all_door_open_time;
    default:
        return 0;
    }
}

uint8_t Get_DoorOpenCloseCounter(DoorTypeId_t typeId)
{
    switch(typeId)
    {
    case DOOR_REF_LEFT:
        return emmulator.ref_left_door_open_count;
    case DOOR_REF_RIGHT:
        return emmulator.ref_right_door_open_count;
    case DOOR_FRZ_LEFT:
        return emmulator.frz_left_door_open_count;
    case DOOR_FRZ_RIGHT:
        return emmulator.frz_right_door_open_count;
    case DOOR_REF:
        return emmulator.ref_door_open_count;
    case DOOR_FRZ:
        return emmulator.frz_door_open_count;
    case DOOR_ALL:
        return emmulator.all_door_open_count;
    default:
        return 0;
    }

}

void Clear_DoorOpenTimeSecond(DoorTypeId_t typeId)
{
    switch(typeId)
    {
    case DOOR_REF_LEFT:
         emmulator.ref_left_door_open_count = 0;
         break;
    case DOOR_REF_RIGHT:
         emmulator.ref_right_door_open_count = 0;
         break;
    case DOOR_FRZ_LEFT:
         emmulator.frz_left_door_open_count = 0;
         break;
    case DOOR_FRZ_RIGHT:
         emmulator.frz_left_door_open_count = 0;
         break;
    case DOOR_REF:
         emmulator.ref_left_door_open_count = 0;
         emmulator.ref_right_door_open_count = 0;
         emmulator.ref_door_open_count = 0;
         break;
    case DOOR_FRZ:
         emmulator.frz_left_door_open_count = 0;
         emmulator.frz_left_door_open_count = 0;
         emmulator.frz_door_open_count = 0;
         break;
    case DOOR_ALL:
         emmulator.ref_left_door_open_count = 0;
         emmulator.ref_right_door_open_count = 0;
         emmulator.ref_door_open_count = 0;
         emmulator.frz_left_door_open_count = 0;
         emmulator.frz_left_door_open_count = 0;
         emmulator.frz_door_open_count = 0;
         emmulator.all_door_open_count = 0;
         break;
    default:
        return;
    }

}

#endif

void Driver_Emulator_Run(void)
{
#if CONIG_EMULATOR_ENABLE
    if(emmulator.fast_forward)
    {
        Shorten_Timer(emmulator.fast_forward);
        emmulator.fast_forward = 0;
    }
#endif
}


