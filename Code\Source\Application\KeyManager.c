/*!
 * @file
 * @brief Manages all the state variables of the key.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "KeyManager.h"
#include "SystemTimerModule.h"
#include "DisplayInterface.h"
#include "Parameter_TemperatureZone.h"
#include "DisplayUsart.h"
#include "LedController.h"
#include "SpecialKeyManager.h"
#include "SystemManager.h"
#include "VerticalBeamHeater.h"
#include "miio_api.h"
#include "Driver_Flash.h"
#include "Drive_Valve.h"
#include "ResolverDevice.h"
#include "Driver_DoorSwitch.h"
#include "FridgeRunner.h"


// data received twice
#define U16_RELEASE_KEY_DELAY_TIME_MSEC ((uint16_t)41)
#define U16_START_UP_TIME_SEC ((uint16_t)5)
#define U16_POWER_ON_TIME_SEC ((uint16_t)60)
#define U16_POWER_ON_TIME_120SEC ((uint16_t)120)
#define U16_NO_KEY_PRESS_TIME_SEC ((uint16_t)25)
#define U16_HOLD_3S_KEY_DELAY_TIME_MSEC ((uint16_t)2500)
#define U16_HOLD_5S_KEY_DELAY_TIME_MSEC ((uint16_t)5000)
#define U16_HOLD_7S_KEY_DELAY_TIME_MSEC ((uint16_t)7000)
#define U16_CONFIRM_SETTING_5S_TIME_MSEC ((uint16_t)5000)

// Long press all key 3s
#define MULTI_KEY_SHOWROOM          (KEY_VALUE_REF + KEY_VALUE_FRZ + KEY_VALUE_MODE)
// Long press all key 5s
#define MULTI_KEY_SELFCHECKING      (KEY_VALUE_REF + KEY_VALUE_FRZ)
#define MULTI_KEY_RESTART_CHECKING  (KEY_VALUE_REF + KEY_VALUE_FRZ)
#define MULTI_KEY_HEATER            (KEY_VALUE_REF + KEY_VALUE_MODE)
#define MULTI_KEY_SKIP_FAC          (KEY_VALUE_BABY + KEY_VALUE_MODE)
#define MULTI_KEY_FORCE_ION_G_WORK  (KEY_VALUE_REF + KEY_VALUE_BABY)


STATIC KeyAction_st st_KeyAction;
static bool b_ForceHeaterFuncOff = false;
static bool b_SelfChecking = false;
static bool b_ForceIonGeneratorWork = false;

static void InvalidKey(void)
{
    st_KeyAction.b_KeyWorked = true;
    Set_MusicType((MusicType_t)eInvalid_Tone);
}

void SetOver(void)
{
    st_KeyAction.b_KeyWorked = true;
}

static void SetOverSave(void)
{
    SetOver();
}

static void Enter_RefSetting(void)
{
    UserMode_t user_mode = Get_UserMode();

    if(false == st_KeyAction.b_RefAdjust)
    {
        st_KeyAction.b_RefAdjust = true;
        st_KeyAction.u8_RefSetpoint = Get_RefSetTemp();
    }
    else
    {
        if(U8_REF_LEVEL_MAX <= st_KeyAction.u8_RefSetpoint)
        {
            st_KeyAction.u8_RefSetpoint = U8_REF_LEVEL_MIN;
        }
        else
        {
            st_KeyAction.u8_RefSetpoint++;
        }

        if((UserMode_t)eTurboFreeze_Mode != user_mode)
        {
            //Update_RefSetTempBak(st_KeyAction.u8_RefSetpoint);
            Set_UserMode((UserMode_t)eManual_Mode);
            Set_ModeDisplay(false, (UserMode_t)eManual_Mode);
        }
    }
    Set_NumberDisplay((DisplayZone_t)eDispRef_Zone, true, st_KeyAction.u8_RefSetpoint);
    Set_MusicType((MusicType_t)eShort_Tone);
    SetOver();
}

static void Confirm_RefSetting(void)
{
    /*if(true == st_KeyAction.b_RefAdjust)
    {
        st_KeyAction.b_RefAdjust = false;
        if(Get_RefSetTemp() != st_KeyAction.u8_RefSetpoint)
        {
            Update_RefSetTemp(st_KeyAction.u8_RefSetpoint);
            Set_NumberDisplay((DisplayZone_t)eDispRef_Zone, false, st_KeyAction.u8_RefSetpoint);
        }
    }*/

    UserMode_t user_mode = Get_UserMode();
    uint8_t ref_setpoint = Get_RefSetTempBak();

    if(true == st_KeyAction.b_RefAdjust)
    { 
        st_KeyAction.b_RefAdjust = false;
        Set_NumberDisplay((DisplayZone_t)eDispRef_Zone, false, st_KeyAction.u8_RefSetpoint); 
        if(Get_RefSetTemp() != st_KeyAction.u8_RefSetpoint)
        { 
            if(ref_setpoint != st_KeyAction.u8_RefSetpoint)
            {
                Update_RefSetTempBak(st_KeyAction.u8_RefSetpoint); //实际挡位设置成手动挡
            }
            if(((UserMode_t)eFuzzy_Mode == user_mode) || ((UserMode_t)eTurboCool_Mode == user_mode))
            { //手动
                Set_UserMode((UserMode_t)eManual_Mode); // 返回手动设定值
                Set_ModeDisplay(false, (UserMode_t)eManual_Mode); //不闪
            }
            Update_RefSetTemp(st_KeyAction.u8_RefSetpoint);
        }
    }
}

static void Enter_FrzSetting(void)
{
    UserMode_t user_mode = Get_UserMode();

    if(false == st_KeyAction.b_FrzAdjust)
    {
        st_KeyAction.b_FrzAdjust = true;
        st_KeyAction.u8_FrzSetpoint = Get_FrzSetTemp();
    }
    else
    {
        if(U8_FRZ_ON_OFFLEVEL_MIN >= st_KeyAction.u8_FrzSetpoint)
        {
            st_KeyAction.u8_FrzSetpoint = U8_FRZ_LEVEL_MAX;
        }
        else
        {
            st_KeyAction.u8_FrzSetpoint--;
        }
        if((UserMode_t)eTurboCool_Mode != user_mode)
        {
            //Update_FrzSetTempBak(st_KeyAction.u8_FrzSetpoint);
            Set_UserMode((UserMode_t)eManual_Mode);
            Set_ModeDisplay(false, (UserMode_t)eManual_Mode);
        }
    }
    Set_NumberDisplay((DisplayZone_t)eDispFrz_Zone, true, st_KeyAction.u8_FrzSetpoint);
    Set_MusicType((MusicType_t)eShort_Tone);
    SetOver();
}

static void Confirm_FrzSetting(void)
{
    /*if(true == st_KeyAction.b_FrzAdjust)
    {
        st_KeyAction.b_FrzAdjust = false;
        if(Get_FrzSetTemp() != st_KeyAction.u8_FrzSetpoint)
        {
            Update_FrzSetTemp(st_KeyAction.u8_FrzSetpoint);
            Set_NumberDisplay((DisplayZone_t)eDispFrz_Zone, false, st_KeyAction.u8_FrzSetpoint);
        }
    }*/

    UserMode_t user_mode = Get_UserMode();
    uint8_t frz_setpoint = Get_FrzSetTempBak();

    if(true == st_KeyAction.b_FrzAdjust)
    {
        st_KeyAction.b_FrzAdjust = false;
        Set_NumberDisplay((DisplayZone_t)eDispFrz_Zone, false, st_KeyAction.u8_FrzSetpoint);
        
        if(Get_FrzSetTemp() != st_KeyAction.u8_FrzSetpoint)
        {
            if(frz_setpoint != st_KeyAction.u8_FrzSetpoint)
            {
                Update_FrzSetTempBak(st_KeyAction.u8_FrzSetpoint);
            }
            if(((UserMode_t)eFuzzy_Mode == user_mode) || ((UserMode_t)eTurboFreeze_Mode == user_mode))
            {
                Set_UserMode((UserMode_t)eManual_Mode);
                Set_ModeDisplay(false, (UserMode_t)eManual_Mode); //不闪
            } //确认设置手动
            Update_FrzSetTemp(st_KeyAction.u8_FrzSetpoint);
        }
    }
}

static void Enter_ModeSetting(void)
{
    uint8_t ref_set_bak = 0;

    if(false == st_KeyAction.b_ModeAdjust)
    {
        st_KeyAction.b_ModeAdjust = true;
        st_KeyAction.u8_ModeSetpoint = Get_UserMode();
    }
    else
    {
        if((UserMode_t)eTurboFreeze_Mode <= st_KeyAction.u8_ModeSetpoint)
        {
            st_KeyAction.u8_ModeSetpoint = (UserMode_t)eManual_Mode;
        }
        else
        {
            st_KeyAction.u8_ModeSetpoint++;
        }
    }
    Set_ModeDisplay(true, st_KeyAction.u8_ModeSetpoint);
    switch(st_KeyAction.u8_ModeSetpoint)
    {
        case(UserMode_t)eFuzzy_Mode:
            Set_NumberDisplay((DisplayZone_t)eDispRef_Zone, false, REF_LEVEL_5);
            break;
        case(UserMode_t)eTurboCool_Mode:
            Set_NumberDisplay((DisplayZone_t)eDispRef_Zone, false, REF_LEVEL_2);
            break;
        case(UserMode_t)eTurboFreeze_Mode:
            Set_NumberDisplay((DisplayZone_t)eDispFrz_Zone, false, FRZ_LEVEL_F30);
            break;
        case(UserMode_t)eManual_Mode:
            ref_set_bak = Get_RefSetTempBak();
            Set_NumberDisplay((DisplayZone_t)eDispRef_Zone, false, ref_set_bak);
            break;
        default:
            break;
    }
    Set_MusicType((MusicType_t)eShort_Tone);
    SetOver();
}

static void Confirm_ModeSetting(void)
{
    if(true == st_KeyAction.b_ModeAdjust)
    {
        st_KeyAction.b_ModeAdjust = false;
        if(Get_UserMode() != st_KeyAction.u8_ModeSetpoint)
        {
            Set_UserMode(st_KeyAction.u8_ModeSetpoint);
            Set_ModeDisplay(false, st_KeyAction.u8_ModeSetpoint);
        }
    }
}

static void Enter_RefVarSetting(void)
{
    if(false == st_KeyAction.b_RefVarAdjust)
    {
        st_KeyAction.b_RefVarAdjust = true;
        st_KeyAction.u8_RefVarSetpoint = Get_RefVarSetTemp();
    }
    else
    {
        if((RefVarSet_t)eRefVar_Zero <= st_KeyAction.u8_RefVarSetpoint)
        {
            st_KeyAction.u8_RefVarSetpoint = (RefVarSet_t)eRefVar_Treasure;
        }
        else
        {
            st_KeyAction.u8_RefVarSetpoint++;
        }
    }
    Set_RefVarDisplay(true, st_KeyAction.u8_RefVarSetpoint);
    Set_MusicType((MusicType_t)eShort_Tone);
    SetOver();
}

static void Confirm_RefVarSetting(void)
{
    if(true == st_KeyAction.b_RefVarAdjust)
    {
        st_KeyAction.b_RefVarAdjust = false;
        if(Get_RefVarSetTemp() != st_KeyAction.u8_RefVarSetpoint)
        {
            Update_RefVarSetTemp(st_KeyAction.u8_RefVarSetpoint);
            Set_RefVarDisplay(false, st_KeyAction.u8_RefVarSetpoint);
        }
    }
}

static void ProcessReleaseKey(void)
{
    bool special_mode = Get_SpecialMode();

    if(true == st_KeyAction.b_KeyLock)
    {
        switch(st_KeyAction.u16_ValidKeyValue)
        {
            case KEY_VALUE_REF:
                st_KeyAction.u8_RefSetpoint = Get_RefSetTemp();
                Set_NumberDisplay((DisplayZone_t)eDispRef_Zone, 0, st_KeyAction.u8_RefSetpoint);
                break;
            case KEY_VALUE_FRZ:
                st_KeyAction.u8_FrzSetpoint = Get_FrzSetTemp();
                Set_NumberDisplay((DisplayZone_t)eDispFrz_Zone, 0, st_KeyAction.u8_FrzSetpoint);
                break;
            default:
                break;
        }
        Set_LockDisplay(0);
        InvalidKey();
    }
    else
    {
        if(true == special_mode)
        {
            /*if(KEY_VALUE_FRZ == st_KeyAction.u16_ValidKeyValue)
            {
                Adjust_SpecialMode();
            }
            */
            switch(st_KeyAction.u16_ValidKeyValue)
            {
                case KEY_VALUE_REF:
                    Inc_AdjustPara();
                    break;
                case KEY_VALUE_FRZ:
                    Adjust_SpecialMode();
                    break;
                case KEY_VALUE_MODE:
                    Confirm_AdjustPara();
                    break;
                case KEY_VALUE_BABY:
                    Dec_AdjustPara();
                    break;
                default:
                    break;
            }
            
        }
        else
        {
            switch(st_KeyAction.u16_ValidKeyValue)
            {
                case KEY_VALUE_REF:
                    Confirm_FrzSetting();
                    Confirm_ModeSetting();
                    Confirm_RefVarSetting();
                    Enter_RefSetting();
                    break;
                case KEY_VALUE_FRZ:
                    Confirm_RefSetting();
                    Confirm_ModeSetting();
                    Confirm_RefVarSetting();
                    Enter_FrzSetting();
                    break;
                case KEY_VALUE_MODE:
                    Confirm_RefSetting();
                    Confirm_FrzSetting();
                    Confirm_RefVarSetting();
                    Enter_ModeSetting();
                    break;
                case KEY_VALUE_BABY:
                    Confirm_RefSetting();
                    Confirm_FrzSetting();
                    Confirm_ModeSetting();
                    Enter_RefVarSetting();
                    break;
                default:
                    break;
            }
        }
    }
}

static void Deal_ShowRoomMode(void)
{
    uint16_t poweron_second = Get_SecondCount();
    bool b_doorSwitchState = Get_DoorSwitchState(DOOR_REF);

    if(poweron_second > U16_POWER_ON_TIME_120SEC)
    {
        return;
    }
    if(false == b_doorSwitchState)
    {
        return;
    }
    Set_ShowRoomDisplay();
    FridgeState_Update(eFridge_Showroom);
    Set_MusicType((MusicType_t)eLong_Tone);
    SetOver();
}

static void Deal_HeaterKey(void)
{
    if(b_ForceHeaterFuncOff)
    {
        b_ForceHeaterFuncOff = false;
        Set_ForceHeaterDisplay(false);
        Forced_VerticalBeamHeaterState(false);
    }
    else if(false == Get_SpecialMode())
    {
        b_ForceHeaterFuncOff = true;
        Set_ForceHeaterDisplay(true);
        Forced_VerticalBeamHeaterState(true);
    }
    Set_MusicType((MusicType_t)eLong_Tone);
    SetOver();
}

static void Deal_IonGeneratorKey(void)
{
    if(b_ForceIonGeneratorWork)
    {
        b_ForceIonGeneratorWork = false;
        Set_ForceIonGeneratorDisplay(false);
        Forced_IonGeneratorState(false);
    }
    else if(false == Get_SpecialMode())
    {
        b_ForceIonGeneratorWork = true;
        Set_ForceIonGeneratorDisplay(true);
        Forced_IonGeneratorState(true);
    }
    Set_MusicType((MusicType_t)eLong_Tone);
    SetOver();
}

static void Deal_RestartChecking(void)
{
    uint16_t poweron_second = Get_SecondCount();

    if(poweron_second <= U16_START_UP_TIME_SEC)
    {
        return;
    }

    if(poweron_second > U16_POWER_ON_TIME_SEC)
    {
        return;
    }
    Set_FactoryResetDisplay();
    Set_FactoryEntryNumber(0);
    SetSysParam(SYSPARAM_INSPECTION, 0); //

    Update_RefSetTempBak(REF_LEVEL_5);	//ADD
    Update_FrzSetTempBak(FRZ_LEVEL_F18);	//ADD

    Update_RefVarSetTemp(eRefVar_Treasure);
    Set_UserMode(eFuzzy_Mode);

    if(IsValveAlreadyReset() == false)
    {
        Drive_ValveReset();
    }
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_AllOpen);
            
    Set_MusicType((MusicType_t)eLong_Tone);
    SetOver();
}

static void Deal_ForceSkipFac(void)
{
    uint16_t poweron_second = Get_SecondCount();

    if(poweron_second <= U16_START_UP_TIME_SEC)
    {
        return;
    }

    if(poweron_second > U16_POWER_ON_TIME_SEC)
    {
        //return;
    }
    Set_FactorySkipDisplay();
    Set_FactoryEntryNumber(1);
    SetSysParam(SYSPARAM_INSPECTION, 1); //
    Set_MusicType((MusicType_t)eLong_Tone);
    if(Get_FridgeState() == eFridge_Factory)
    {
        //display.b_reboot_wifi = true;
        execute_wifi_cmd_async(WIFI_CMD_REBOOT, NULL);
        FridgeState_Update((FridgeState_t)eFridge_Startup);
    }
    SetOver();
}


static void ProcessPcbTestKey(void)
{
    static uint8_t u8_PcbTestModeCount;
    uint16_t poweron_second = Get_SecondCount();

    if(poweron_second > U16_POWER_ON_TIME_SEC)
    {
        return;
    }

    if(true == Get_SpecialMode())
    {
        return;
    }

    if(0 == u8_PcbTestModeCount)
    {
        st_KeyAction.u16_PcbTestTimer = Get_SecondCount();
    }

    if((false == b_SelfChecking) && (true == st_KeyAction.b_KeyLock))
    {
        switch(st_KeyAction.u16_ValidKeyValue)
        {
            case KEY_VALUE_REF:
                if(u8_PcbTestModeCount == 0)
                {
                    u8_PcbTestModeCount = 1;
                }
                else
                {
                    u8_PcbTestModeCount = 0;
                }
                break;
            case KEY_VALUE_FRZ:
                if(u8_PcbTestModeCount == 1)
                {
                    u8_PcbTestModeCount = 2;
                }
                else
                {
                    u8_PcbTestModeCount = 0;
                }
                break;
            case KEY_VALUE_BABY:
                if(u8_PcbTestModeCount == 2)
                {
                    u8_PcbTestModeCount = 3;
                }
                else
                {
                    u8_PcbTestModeCount = 0;
                }
                break;
            case KEY_VALUE_MODE:
                if(u8_PcbTestModeCount == 3)
                {
                    b_SelfChecking = true;
                    Set_PcbTestDisplay();
                }
                else
                {
                    u8_PcbTestModeCount = 0;
                }
                break;
            default:
                break;
        }
    }
}

static void Deal_SelfChecking(void)
{
    uint16_t poweron_second = Get_SecondCount();

    if(poweron_second > U16_POWER_ON_TIME_SEC)
    {
        return;
    }

    // FridgeState_Update((FridgeState_t)eFridge_SelfChecking);
    SetSysParam(SYSPARAM_INSPECTION, 0);
    Set_MusicType((MusicType_t)eLong_Tone);
    SetOver();
}

static void ProcessHold3sKey(void)
{
    switch(st_KeyAction.u16_ValidKeyValue)
    {
        case KEY_VALUE_MODE:
            if(true == st_KeyAction.b_KeyLock)
            {
                st_KeyAction.b_KeyLock = false;
            }
            else
            {
                Confirm_RefSetting();
                Confirm_FrzSetting();
                Confirm_ModeSetting();
                Confirm_RefVarSetting();
                st_KeyAction.b_KeyLock = true;
            }
            Set_MusicType((MusicType_t)eLong_Tone);
            SetOver();
            break;

        case MULTI_KEY_SKIP_FAC:
            if(false == st_KeyAction.b_KeyLock)
            {
                Deal_ForceSkipFac();
            }
            break;

        case MULTI_KEY_RESTART_CHECKING:
            if(false == st_KeyAction.b_KeyLock)
            {
                Deal_RestartChecking();
            }
            break;
			
        case MULTI_KEY_SHOWROOM:
            if(false == st_KeyAction.b_KeyLock)
            {
                Deal_ShowRoomMode();
            }
            break;
			
        default:
            break;
    }
}

static void ProcessHold5sKey(void)
{
    if(true == st_KeyAction.b_KeyLock)
    {
        switch(st_KeyAction.u16_ValidKeyValue)
        {
            case MULTI_KEY_HEATER:
                Deal_HeaterKey();
                break;
                
            case MULTI_KEY_FORCE_ION_G_WORK:
                Deal_IonGeneratorKey();
                break;
            //case MULTI_KEY_RESTART_CHECKING:
            //	Deal_RestartChecking();
            //    break;
            default:
                break;
        }
    }
    else
    {
        switch(st_KeyAction.u16_ValidKeyValue)
        {
            //case MULTI_KEY_SELFCHECKING:
            //    Deal_SelfChecking();
            //    break;
            default:
                break;
        }
    }
}

static void ProcessHold7sKey(void)
{
    switch(st_KeyAction.u16_ValidKeyValue)
    {
        case KEY_VALUE_REF:
            if(false == st_KeyAction.b_WifiReset)
            {
                st_KeyAction.b_WifiReset = true;
            }
            execute_wifi_cmd_async(WIFI_CMD_RESTORE, NULL);
            Set_MusicType((MusicType_t)eLong_Tone);
            SetOver();
            break;
        default:
            break;
    }
}

static void ProcessPressKey(void)
{
    if(Get_UiState() == (UiState_t)eUiSleep_State)
    {
        st_KeyAction.b_KeyWorked = true;
    }
    Wakeup_UserInterface();

    st_KeyAction.u16_NoKeyTimer = Get_SecondCount();
}

static void KeyAction(void)
{
    st_KeyAction.u16_CurrentKeyValue = Get_DisplayKeyValue();

    if(st_KeyAction.u16_PreviousKeyValue != st_KeyAction.u16_CurrentKeyValue)
    {
        st_KeyAction.u16_PreviousKeyValue = st_KeyAction.u16_CurrentKeyValue;
        st_KeyAction.u16_ReleaseKeyTimer = Get_MSecCount();
    }
    else if(Get_MSecElapsedTime(st_KeyAction.u16_ReleaseKeyTimer) >= U16_RELEASE_KEY_DELAY_TIME_MSEC)
    {
        if(false == st_KeyAction.b_KeyWorked)
        {
            if((st_KeyAction.u16_PreviousKeyValue == 0) && (st_KeyAction.u16_ValidKeyValue != 0))
            {
                ProcessReleaseKey();
                ProcessPcbTestKey();
            }
        }

        if(st_KeyAction.u16_ValidKeyValue != st_KeyAction.u16_PreviousKeyValue)
        {
            st_KeyAction.u16_ValidKeyValue = st_KeyAction.u16_PreviousKeyValue;
            st_KeyAction.u16_HoldKeyTimer = Get_MSecCount();
        }
    }

    if(0 == st_KeyAction.u16_ValidKeyValue)
    {
        st_KeyAction.b_KeyWorked = false;
        st_KeyAction.u16_HoldKeyTimer = Get_MSecCount();
    }
    else if(false == st_KeyAction.b_KeyWorked)
    {
        if(Get_MSecElapsedTime(st_KeyAction.u16_HoldKeyTimer) >= U16_HOLD_7S_KEY_DELAY_TIME_MSEC)
        {
            ProcessHold7sKey();
        }
        else if(Get_MSecElapsedTime(st_KeyAction.u16_HoldKeyTimer) >= U16_HOLD_5S_KEY_DELAY_TIME_MSEC)
        {
            ProcessHold5sKey();
        }
        else if(Get_MSecElapsedTime(st_KeyAction.u16_HoldKeyTimer) >= U16_HOLD_3S_KEY_DELAY_TIME_MSEC)
        {
            ProcessHold3sKey();
        }

        ProcessPressKey();
    }

    Ctrl_SpecialKey(st_KeyAction.u16_ValidKeyValue);

    if((Get_SecondElapsedTime(st_KeyAction.u16_NoKeyTimer) >= U16_NO_KEY_PRESS_TIME_SEC)
        && (false == Get_SpecialMode()))
    {
        st_KeyAction.b_KeyLock = true;
    }
}

void Handle_KeyManager(void)
{
    KeyAction();
}

void Set_LockStatus(bool status)
{
    st_KeyAction.b_KeyLock = status;
    st_KeyAction.b_RefAdjust = false;
    st_KeyAction.b_RefVarAdjust = false;
    st_KeyAction.b_FrzAdjust = false;
    st_KeyAction.b_ModeAdjust = false;
}

bool Get_LockStatus(void)
{
    return (st_KeyAction.b_KeyLock);
}

void Clear_ForceIonGeneratorWorkState(void)
{
    b_ForceIonGeneratorWork = false;
}
