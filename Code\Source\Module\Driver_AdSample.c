/*!
 * @file
 * @brief This module is the temperature sensor sample driver.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <math.h>
#include "Driver_AdSample.h"
#include "Adpt_ADC.h"
#include "Core_Types.h"
#include "SystemTimerModule.h"
#include "Defrosting.h"
#include "Driver_Emulator.h"

#define U8_ADC_SAMPLE_COUNT ((uint8_t)66)
#define U8_ADC_SAMPLE_SHIFT_BIT ((uint8_t)6)
#define U16_ADC_UPPER_LIMIT ((uint16_t)1000) // ADC上限值
#define U16_ADC_LOWER_LIMIT ((uint16_t)20) // ADC下限值
#define U16_TEMPRETURE_OFFSET ((uint16_t)500)
#define U16_ADC10BITVALUE ((uint16_t)1023)
#define U16_SERIESRESISTOR ((uint16_t)5100)
#define U16_ROOM_SAMPLE_SERIESRESISTOR ((uint16_t)10000)
#define U16_ROOM_RANGE_CHANGE_TIMEOUT ((uint16_t)1200)

#define U16_ZONE_TEMP_LOOK_UP_LOW_INDEX ((uint16_t)127)
#define U16_ZONE_TEMP_LOOK_UP_HIGH_INDEX ((uint16_t)984)
#define U16_ZONE_TEMP_SIZEOF_LOOKUPTABLE ((uint16_t)(NUM_ELEMENTS(ary_Lntd506Temp)))

#define U16_ROOM_TEMP_LOOK_UP_LOW_INDEX ((uint16_t)235)
#define U16_ROOM_TEMP_LOOK_UP_HIGH_INDEX ((uint16_t)996)
#define U16_ROOM_TEMP_SIZEOF_LOOKUPTABLE ((uint16_t)(NUM_ELEMENTS(ary_RoomTemp)))
#define U16_ROOM_TEMP_OFFSET ((uint16_t)10)

static bool f_FirstAd;
static uint8_t u8_SampleCount;
static bool b_BelowEightDegree = false;
static bool b_BelowEightDegreeChange = false;

AdSample_st st_AdSample[(uint8_t)SENSOR_TYPE_MAX];
RoomTempRange_t roomTempRange = RT_BELOW35;
RoomTempRange_t roomTempRangeChange = RT_BELOW35;

RoomTempRangeET_t roomTempRangeET = RT2_UP13;
RoomTempRangeET_t roomTempRangeChangeET = RT2_UP13;

HumidityRange_t humidityRange = HBELOW90;
uint16_t u16_RoomRangeTime1SecCount;
uint16_t u16_BelowEightDegreeTime1SecCount;
uint16_t u16_RoomRangeTime1SecCountET;

uint16_t u16_12V_Ad;
uint16_t u16_220V_Ad;
float result_12V_LPF = 0;
float result_12V_LPF2 = 0;
uint16_t u16_12V_Power;

static const CalRoomRange_st ARY_RoomRange[(uint8_t)RT_MAXSIZE] = {
    { RT_BELOW13, CON_13P0_DEGREE - 10, CON_13P0_DEGREE + 10 },
    { RT_BELOW18, CON_18P0_DEGREE - 10, CON_18P0_DEGREE + 10 },
    { RT_BELOW23, CON_23P0_DEGREE - 10, CON_23P0_DEGREE + 10 },
    { RT_BELOW28, CON_28P0_DEGREE - 10, CON_28P0_DEGREE + 10 },
    { RT_BELOW35, CON_35P0_DEGREE - 10, CON_35P0_DEGREE + 10 },
    { RT_BELOW40, CON_40P0_DEGREE - 10, CON_40P0_DEGREE + 10 },
    { RT_UP40, MAX_UINT16, MAX_UINT16 }
};

static const CalRoomRange_st ARY_RoomRangeET[(uint8_t)RT2_MAXSIZE] = {
    { RT2_BELOW8,  CON_8P0_DEGREE - 10,  CON_8P0_DEGREE + 10 },
    { RT2_BELOW13, CON_13P0_DEGREE - 10, CON_13P0_DEGREE + 10 },
    { RT2_UP13, MAX_UINT16, MAX_UINT16 }
};

const CalHumidityRange_st ARY_HumidityRange[(uint8_t)HUMIDITY_MAXSIZE] = {
    { HBELOW10, HUMIDITY_AD_PCT10 - 5, HUMIDITY_AD_PCT10 },
    { HBELOW15, HUMIDITY_AD_PCT15 - 5, HUMIDITY_AD_PCT15 },
    { HBELOW20, HUMIDITY_AD_PCT20 - 5, HUMIDITY_AD_PCT20 },
    { HBELOW25, HUMIDITY_AD_PCT25 - 5, HUMIDITY_AD_PCT25 },
    { HBELOW30, HUMIDITY_AD_PCT30 - 5, HUMIDITY_AD_PCT30 },
    { HBELOW35, HUMIDITY_AD_PCT35 - 5, HUMIDITY_AD_PCT35 },
    { HBELOW40, HUMIDITY_AD_PCT40 - 5, HUMIDITY_AD_PCT40 },
    { HBELOW45, HUMIDITY_AD_PCT45 - 5, HUMIDITY_AD_PCT45 },
    { HBELOW50, HUMIDITY_AD_PCT50 - 5, HUMIDITY_AD_PCT50 },
    { HBELOW55, HUMIDITY_AD_PCT55 - 5, HUMIDITY_AD_PCT55 },
    { HBELOW60, HUMIDITY_AD_PCT60 - 5, HUMIDITY_AD_PCT60 },
    { HBELOW65, HUMIDITY_AD_PCT65 - 5, HUMIDITY_AD_PCT65 },
    { HBELOW70, HUMIDITY_AD_PCT70 - 5, HUMIDITY_AD_PCT70 },
    { HBELOW75, HUMIDITY_AD_PCT75 - 5, HUMIDITY_AD_PCT75 },
    { HBELOW80, HUMIDITY_AD_PCT80 - 5, HUMIDITY_AD_PCT80 },
    { HBELOW85, HUMIDITY_AD_PCT85 - 5, HUMIDITY_AD_PCT85 },
    { HBELOW90, HUMIDITY_AD_PCT90 - 5, HUMIDITY_AD_PCT90 },
    { HBELOW95, HUMIDITY_AD_PCT95 - 5, HUMIDITY_AD_PCT95 },
    { HUP95, MAX_UINT16, MAX_UINT16 }
};

static void Get_AdConvertedValue(void)
{
    uint8_t sensor_type = 0;
    uint16_t ad_value = 0;

    for(sensor_type = 0; sensor_type < ((uint8_t)SENSOR_TYPE_MAX); sensor_type++)
    {
        ad_value = ADC_GetResult(sensor_type);

        if(((uint8_t)SENSOR_DC) == sensor_type)
        {
            u16_12V_Ad = ad_value;
            result_12V_LPF = (((float)ad_value) + result_12V_LPF * 15) * 0.0625;
            result_12V_LPF2 = (result_12V_LPF + result_12V_LPF2 * 15) * 0.0625;
            //u16_12V_Power = (uint16_t)((7.448 * result_12V_LPF2 + 0.3217) * 0.144);
            u16_12V_Power = (uint16_t)((7.448 * result_12V_LPF2 + 0.3217) * 0.104);
        }

        if(((uint8_t)SENSOR_AC) == sensor_type)
        {
            u16_220V_Ad = ad_value;
        }

        if(u8_SampleCount == 0)
        {
            st_AdSample[sensor_type].u32_SumAd = (uint32_t)ad_value;
            st_AdSample[sensor_type].u16_MaxAd = ad_value;
            st_AdSample[sensor_type].u16_MinAd = ad_value;
        }
        else
        {
            st_AdSample[sensor_type].u32_SumAd += (uint32_t)ad_value;
        }

        if(ad_value > st_AdSample[sensor_type].u16_MaxAd)
        {
            st_AdSample[sensor_type].u16_MaxAd = ad_value;
        }

        if(ad_value < st_AdSample[sensor_type].u16_MinAd)
        {
            st_AdSample[sensor_type].u16_MinAd = ad_value;
        }
    }
    ADC_Restart();
}

// R5 - 5.060KΩ - 2%；B5/25 = 3839K- 2%
static void Convert_AdToZoneTemperature(uint8_t u8_SensorType, uint16_t u16_AdValue)
{
    uint16_t u16_Index = 0;

    u16_Index = u16_AdValue;

    // ad count is out the range
    if(u16_Index < U16_ZONE_TEMP_LOOK_UP_LOW_INDEX)
    {
        u16_Index = U16_ZONE_TEMP_LOOK_UP_LOW_INDEX;
    }
    else if(u16_Index > U16_ZONE_TEMP_LOOK_UP_HIGH_INDEX)
    {
        u16_Index = U16_ZONE_TEMP_LOOK_UP_HIGH_INDEX;
    }

    // ad count is with in the range
    u16_Index = u16_Index - U16_ZONE_TEMP_LOOK_UP_LOW_INDEX;

    if(u16_Index >= U16_ZONE_TEMP_SIZEOF_LOOKUPTABLE)
    {
        u16_Index = U16_ZONE_TEMP_SIZEOF_LOOKUPTABLE - 1;
    }

    st_AdSample[u8_SensorType].u16_SensorValue = ary_Lntd506Temp[u16_Index];
}

// R25 - 10KΩ ；B25/50 - 3380K
static void Convert_AdToRoomTemperature(uint8_t u8_SensorType, uint16_t u16_AdValue)
{
    uint16_t u16_index = 0;

    u16_index = u16_AdValue;

    // ad count is out the range
    if(u16_index < U16_ROOM_TEMP_LOOK_UP_LOW_INDEX)
    {
        u16_index = U16_ROOM_TEMP_LOOK_UP_LOW_INDEX;
    }
    else if(u16_index > U16_ROOM_TEMP_LOOK_UP_HIGH_INDEX)
    {
        u16_index = U16_ROOM_TEMP_LOOK_UP_HIGH_INDEX;
    }

    // ad count is with in the range
    u16_index = u16_index - U16_ROOM_TEMP_LOOK_UP_LOW_INDEX;

    if(u16_index >= U16_ROOM_TEMP_SIZEOF_LOOKUPTABLE)
    {
        u16_index = U16_ROOM_TEMP_SIZEOF_LOOKUPTABLE - 1;
    }

    st_AdSample[u8_SensorType].u16_SensorValue = ary_RoomTemp[u16_index];
    // offset 1 degree C
    //st_AdSample[u8_SensorType].u16_SensorValue -= U16_ROOM_TEMP_OFFSET;
    if(st_AdSample[u8_SensorType].u16_SensorValue < CON_18P0_DEGREE)
    {
        st_AdSample[u8_SensorType].u16_SensorValue -= U16_ROOM_TEMP_OFFSET;//补偿-1℃
    }
    else if(st_AdSample[u8_SensorType].u16_SensorValue > CON_18P5_DEGREE)//加0.5℃回差值
    {
        st_AdSample[u8_SensorType].u16_SensorValue += 0;//不补偿
    }
}

static void Process_SensorValue(uint8_t u8_SensorType)
{
    switch(u8_SensorType)
    {
        case((uint8_t)SENSOR_REF):
        case((uint8_t)SENSOR_FRZ):
        case((uint8_t)SENSOR_VV):
        case((uint8_t)SENSOR_REF_DEFROST):
        case((uint8_t)SENSOR_DEFROST):
            Convert_AdToZoneTemperature(u8_SensorType, st_AdSample[u8_SensorType].u16_FilteredAd);
            break;
        case((uint8_t)SENSOR_ROOM):
            Convert_AdToRoomTemperature(u8_SensorType, st_AdSample[u8_SensorType].u16_FilteredAd);
            break;
        case((uint8_t)SENSOR_HUMIDITY):
        case((uint8_t)SENSOR_AC):
        case((uint8_t)SENSOR_DC):
            st_AdSample[u8_SensorType].u16_SensorValue = st_AdSample[u8_SensorType].u16_FilteredAd;
            break;
        default:
            break;
    }
}

static void Judge_SensorError(uint8_t u8_SensorType)
{
    switch(u8_SensorType)
    {
        case((uint8_t)SENSOR_REF):
        case((uint8_t)SENSOR_FRZ):
        case((uint8_t)SENSOR_VV):
        case((uint8_t)SENSOR_REF_DEFROST):
        case((uint8_t)SENSOR_DEFROST):
        case((uint8_t)SENSOR_ROOM):
        case((uint8_t)SENSOR_HUMIDITY):
            if((st_AdSample[u8_SensorType].u16_FilteredAd > U16_ADC_UPPER_LIMIT) ||
                (st_AdSample[u8_SensorType].u16_FilteredAd < U16_ADC_LOWER_LIMIT))
            {
                st_AdSample[u8_SensorType].b_SensorError = true;
            }
            else
            {
                st_AdSample[u8_SensorType].b_SensorError = false;
            }
            break;
        default:
            break;
    }
}

static void Judge_RoomTempRange(void)
{
    uint8_t u8_Index;
    uint16_t u16_change_time = 0;
    DefrostMode_t deforst_mode = Get_DefrostMode();

    if(true == st_AdSample[(uint8_t)SENSOR_ROOM].b_SensorError)
    {
        roomTempRange = (uint8_t)RT_BELOW35;
        roomTempRangeChange = (uint8_t)RT_BELOW35;
        b_BelowEightDegree = false;
        b_BelowEightDegreeChange = false;
        roomTempRangeET = (uint8_t)RT2_UP13;
        roomTempRangeChangeET = (uint8_t)RT2_UP13;
    }
    else if(deforst_mode <= eDefrostMode_PreHold)
    {
        for(u8_Index = 0; u8_Index < (uint8_t)RT_MAXSIZE; u8_Index++)
        {
            if(st_AdSample[(uint8_t)SENSOR_ROOM].u16_SensorValue < ARY_RoomRange[u8_Index].u16_LowerLimit)
            {
                roomTempRangeChange = ARY_RoomRange[u8_Index].roomRange;
                break;
            }
            else if(st_AdSample[(uint8_t)SENSOR_ROOM].u16_SensorValue <= ARY_RoomRange[u8_Index].u16_UpperLimit)
            {
                if(roomTempRangeChange <= ARY_RoomRange[u8_Index].roomRange)
                {
                    roomTempRangeChange = ARY_RoomRange[u8_Index].roomRange;
                }
                else
                {
                    roomTempRangeChange = ARY_RoomRange[u8_Index + 1].roomRange;
                }
                break;
            }
        }

        if(b_BelowEightDegree && st_AdSample[(uint8_t)SENSOR_ROOM].u16_SensorValue <= CON_9P0_DEGREE)
        {
            b_BelowEightDegreeChange = true;
        }
        else if(b_BelowEightDegree == false && st_AdSample[(uint8_t)SENSOR_ROOM].u16_SensorValue > CON_7P0_DEGREE)
        {
            b_BelowEightDegreeChange = false;
        }
        else
        {
            b_BelowEightDegreeChange = !b_BelowEightDegree;
        }

        for(u8_Index = 0; u8_Index < (uint8_t)RT2_MAXSIZE; u8_Index++)
        {
            if(st_AdSample[(uint8_t)SENSOR_ROOM].u16_SensorValue < ARY_RoomRangeET[u8_Index].u16_LowerLimit)
            {
                roomTempRangeChangeET = ARY_RoomRangeET[u8_Index].roomRange;
                break;
            }
            else if(st_AdSample[(uint8_t)SENSOR_ROOM].u16_SensorValue <= ARY_RoomRangeET[u8_Index].u16_UpperLimit)
            {
                if(roomTempRangeChangeET <= ARY_RoomRangeET[u8_Index].roomRange)
                {
                    roomTempRangeChangeET = ARY_RoomRangeET[u8_Index].roomRange;
                }
                else
                {
                    roomTempRangeChangeET = ARY_RoomRangeET[u8_Index + 1].roomRange;
                }
                break;
            }
        }
    }

    if(true == f_FirstAd)
    {
        if(roomTempRangeChange != roomTempRange)
        {
            u16_change_time = Get_SecondElapsedTime(u16_RoomRangeTime1SecCount);
            if(u16_change_time >= U16_ROOM_RANGE_CHANGE_TIMEOUT)
            {
                roomTempRange = roomTempRangeChange;
            }
        }
        else
        {
            u16_RoomRangeTime1SecCount = Get_SecondCount();
        }

        if(b_BelowEightDegreeChange != b_BelowEightDegree)
        {
            u16_change_time = Get_SecondElapsedTime(u16_BelowEightDegreeTime1SecCount);
            if(u16_change_time >= U16_ROOM_RANGE_CHANGE_TIMEOUT)
            {
                b_BelowEightDegree = b_BelowEightDegreeChange;
            }
        }
        else
        {
            u16_BelowEightDegreeTime1SecCount = Get_SecondCount();
        }

        if(roomTempRangeChangeET != roomTempRangeET)
        {
            u16_change_time = Get_SecondElapsedTime(u16_RoomRangeTime1SecCountET);
            if(u16_change_time >= U16_ROOM_RANGE_CHANGE_TIMEOUT)
            {
                roomTempRangeET = roomTempRangeChangeET;
            }
        }
        else
        {
            u16_RoomRangeTime1SecCountET = Get_SecondCount();
        }
    }
    else
    {
        roomTempRange = roomTempRangeChange;
        b_BelowEightDegree = b_BelowEightDegreeChange;
        roomTempRangeET = roomTempRangeChangeET;
    }
}

static void Judge_HumidityRange(void)
{
    uint8_t u8_Index;

    if(true == st_AdSample[SENSOR_HUMIDITY].b_SensorError)
    {
        humidityRange = HBELOW90;
    }
    else
    {
        for(u8_Index = 0; u8_Index < (uint8_t)HUMIDITY_MAXSIZE; u8_Index++)
        {
            if(st_AdSample[SENSOR_HUMIDITY].u16_FilteredAd < ARY_HumidityRange[u8_Index].u16_LowerLimit)
            {
                humidityRange = ARY_HumidityRange[u8_Index].humidityRange;
                break;
            }
            else if(st_AdSample[SENSOR_HUMIDITY].u16_FilteredAd <= ARY_HumidityRange[u8_Index].u16_UpperLimit)
            {
                if(humidityRange <= ARY_HumidityRange[u8_Index].humidityRange)
                {
                    humidityRange = ARY_HumidityRange[u8_Index].humidityRange;
                }
                else
                {
                    humidityRange = ARY_HumidityRange[u8_Index + 1].humidityRange;
                }
                break;
            }
        }
    }
}

void AdSample_Handle(void)
{
    uint8_t sensor_type = 0;
    uint32_t cal_ad = 0;

    Get_AdConvertedValue();
    u8_SampleCount++;
    if(u8_SampleCount >= U8_ADC_SAMPLE_COUNT)
    {
        u8_SampleCount = 0;
        for(sensor_type = 0; sensor_type < ((uint8_t)SENSOR_TYPE_MAX); sensor_type++)
        {
            cal_ad =
                st_AdSample[sensor_type].u32_SumAd - st_AdSample[sensor_type].u16_MaxAd - st_AdSample[sensor_type].u16_MinAd;
            st_AdSample[sensor_type].u16_FilteredAd = (uint16_t)(cal_ad >> U8_ADC_SAMPLE_SHIFT_BIT);
            Process_SensorValue(sensor_type);
            Judge_SensorError(sensor_type);
        }
        Judge_RoomTempRange();
        Judge_HumidityRange();

        if(false == f_FirstAd)
        {
            f_FirstAd = true;
        }
    }
}

__EMULATOR__FUNCITON RoomTempRange_t Get_RoomTempRange(void)
{
    return (roomTempRange);
}

__EMULATOR__FUNCITON RoomTempRangeET_t Get_RoomTempRangeET(void)
{
    return (roomTempRangeET);
}

__EMULATOR__FUNCITON HumidityRange_t Get_HumidityRange(void)
{
    return (humidityRange);
}

__EMULATOR__FUNCITON uint16_t Get_SensorValue(SensorType_t type)
{
    return (st_AdSample[type].u16_SensorValue);
}

__EMULATOR__FUNCITON bool Get_SensorError (SensorType_t type)
{
    return (st_AdSample[type].b_SensorError);
}

bool IsRoomTempBelowEightDegree(void)
{
    return b_BelowEightDegree;
}

uint16_t Get_12VPower(void)
{
    return (u16_12V_Power);
}

uint16_t Get_220VAdValue(void)
{
    return (u16_220V_Ad);
}