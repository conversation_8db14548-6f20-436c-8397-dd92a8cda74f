/*!
 * @file
 * @brief Manages all the state variables of the fridge runner.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "FridgeRunner.h"
#include "Core_CallBackTimer.h"
#include "CoolingCycle.h"
#include "Parameter_TemperatureZone.h"
#include "Driver_AdSample.h"
#include "Driver_DoorSwitch.h"
#include "Driver_DoubleDamper.h"
#include "Driver_Fan.h"
#include "IO_Device.h"
#include "Drive_Valve.h"
#include "ResolverDevice.h"
#include "SystemTimerModule.h"
#include "VerticalBeamHeater.h"
#include "DisplayInterface.h"
#include "FaultCode.h"
#include "ParameterManager.h"
#include "SystemManager.h"
#include "InverterUsart.h"
#include "KeyManager.h"


#define FRIDGE_NUM_COUNTS_PER_MINUTE (uint16_t)60
#define U16_FRIDGERUNNER_CYCLE_SECOND (uint16_t)1
#define U16_TEMP_SWINGS_RANGE (uint16_t)20
#define U16_ENTER_ENERGY_MODE_MINUTES (uint16_t)(20 * 60)
#define U16_ENTER_CONDENSATION_MODE_MINUTES (uint16_t)240
#define U16_EXIT_CONDENSATION_MODE_MINUTES (uint16_t)60
#define U16_ROOM_RANGE_EXIT_MINUTES (uint16_t)10
#define U16_ROOM_SWINGS_EXIT_MINUTES (uint16_t)30
#define U16_HUMI_RANGE_EXIT_MINUTES (uint16_t)20
#define U8_ENERGY_MODE_EXIT_DOOR_OPEN_COUNT (uint8_t)5	//GGS  5times
#define U8_ENERGY_MODE_EXIT_REF_DOOR_OPEN_COUNT (uint8_t)5
#define U8_REF_FROST_REDUCE_REF_FAN_DUTY (uint8_t)50
#define U8_REF_FROST_REDUCE_DOOR_OPEN_COUNT (uint8_t)5
#define U8_REF_FROST_REDUCE_RUN_IN_ENERGY_MODE_MINUTES (uint8_t)10
#define U8_VALVE_STAY_REF_FROST_MINUTES (uint8_t)30
#define U16_POWER_ON_DELAY_REF_VAR_COLLING_MINUTES (uint16_t)30
#define U16_ION_GENERATOR_FORCE_CTRL_MINUTES (uint16_t)(24 * 60)

// clang-format off
static DefrostNormalCondition_st ary_DefrostNormalCondition[] = {
    // RT <= 13
    {
        5,
        { // 冰箱时间    温区时间  冷藏门  冷冻门  总开门  退出温度
            { 12 * 60,  0xFFFF,   240,   240,   0xFFFF, CON_10P0_DEGREE },
            { 16 * 60,  0xFFFF,   120,   120,   0xFFFF, CON_8P0_DEGREE },
            { 25 * 60,  0xFFFF,    90,    90,   0xFFFF, CON_8P0_DEGREE },
            { 36 * 60,  0xFFFF,    30,    30,   0xFFFF, CON_8P0_DEGREE },
            { 48 * 60,  0xFFFF,     0,     0,   0xFFFF, CON_1P0_DEGREE },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 } } },
    // 13 < RT <= 35
    {
        7,
        { // 冰箱时间   温区时间  冷藏门  冷冻门  总开门  退出温度
            { 12 * 60,  0xFFFF,  540,   540,   0xFFFF, CON_10P0_DEGREE },
            { 16 * 60,  0xFFFF,  360,   360,   0xFFFF, CON_8P0_DEGREE },
            { 25 * 60,  0xFFFF,  240,   240,   0xFFFF, CON_8P0_DEGREE },
            { 36 * 60,  0xFFFF,  120,   120,   0xFFFF, CON_8P0_DEGREE },
            { 48 * 60,  0xFFFF,   90,    90,   0xFFFF, CON_8P0_DEGREE },
            { 60 * 60,  0xFFFF,   30,    30,   0xFFFF, CON_8P0_DEGREE },
            { 73 * 60,  0xFFFF,    0,     0,   0xFFFF, CON_8P0_DEGREE } } },
    // 35 < RT <= 40
    {
        3,
        { // 冰箱时间   温区时间   冷藏门  冷冻门  总开门  退出温度
            { 12 * 60,  0xFFFF,   360,   240,   0xFFFF, CON_10P0_DEGREE },
            { 16 * 60,  0xFFFF,   120,   120,   0xFFFF, CON_8P0_DEGREE },
            { 25 * 60,  0xFFFF,     0,     0,   0xFFFF, CON_8P0_DEGREE },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 } } },
    // 40 < RT
    {
        3,
        { // 冰箱时间   温区时间   冷藏门  冷冻门  总开门  退出温度
            { 12 * 60,  0xFFFF,   360,   360,   0xFFFF, CON_10P0_DEGREE },
            { 16 * 60,  0xFFFF,   120,   120,   0xFFFF, CON_8P0_DEGREE },
            { 25 * 60,  0xFFFF,     0,     0,   0xFFFF, CON_1P0_DEGREE },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 } } }
};


// FridgeType - France
static DefrostNormalCondition_st ary_DefrostNormalCondition_France[] = {
    // RT <= 13
    {
        5,
        { // 冰箱时间    温区时间  冷藏门  冷冻门  总开门  退出温度
            { 12 * 60,  0xFFFF,   240,   240,   0xFFFF, CON_10P0_DEGREE },
            { 16 * 60,  0xFFFF,   120,   120,   0xFFFF, CON_8P0_DEGREE },
            { 25 * 60,  0xFFFF,    90,    90,   0xFFFF, CON_8P0_DEGREE },
            { 36 * 60,  0xFFFF,    30,    30,   0xFFFF, CON_8P0_DEGREE },
            { 48 * 60,  0xFFFF,     0,     0,   0xFFFF, CON_3P0_DEGREE },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 } } },
    // 13 < RT <= 35
    {
        7,
        { // 冰箱时间   温区时间  冷藏门  冷冻门  总开门  退出温度
            { 12 * 60,  0xFFFF,  540,   540,   0xFFFF, CON_10P0_DEGREE },
            { 16 * 60,  0xFFFF,  360,   360,   0xFFFF, CON_8P0_DEGREE },
            { 25 * 60,  0xFFFF,  240,   240,   0xFFFF, CON_8P0_DEGREE },
            { 36 * 60,  0xFFFF,  120,   120,   0xFFFF, CON_8P0_DEGREE },
            { 48 * 60,  0xFFFF,   90,    90,   0xFFFF, CON_8P0_DEGREE },
            { 60 * 60,  0xFFFF,   30,    30,   0xFFFF, CON_8P0_DEGREE },
            { 73 * 60,  0xFFFF,    0,     0,   0xFFFF, CON_8P0_DEGREE } } },
    // 35 < RT <= 40
    {
        3,
        { // 冰箱时间   温区时间   冷藏门  冷冻门  总开门  退出温度
            { 12 * 60,  0xFFFF,   360,   240,   0xFFFF, CON_10P0_DEGREE },
            { 16 * 60,  0xFFFF,   120,   120,   0xFFFF, CON_8P0_DEGREE },
            { 25 * 60,  0xFFFF,     0,     0,   0xFFFF, CON_8P0_DEGREE },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 } } },
    // 40 < RT
    {
        3,
        { // 冰箱时间   温区时间   冷藏门  冷冻门  总开门  退出温度
            { 12 * 60,  0xFFFF,   360,   360,   0xFFFF, CON_10P0_DEGREE },
            { 16 * 60,  0xFFFF,   120,   120,   0xFFFF, CON_8P0_DEGREE },
            { 25 * 60,  0xFFFF,     0,     0,   0xFFFF, CON_3P0_DEGREE },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 },
            { 00 * 60, 00 * 60,     0,     0,        0,              0 } } }
};

// clang-format on

static const uint8_t ary_RefReduceValveOnRefTime[(uint8_t)RT_MAXSIZE] = {
    //分钟
    20,     // RT_BELOW13
    20,     // RT_BELOW18
    30,     // RT_BELOW23
    30,     // RT_BELOW28
    40,     // RT_BELOW35
    40,    // RT_BELOW40
    60     // RT_UP40
};

enum
{
    Signal_Entry = SimpleFsmSignal_Entry,
    Signal_Exit = SimpleFsmSignal_Exit,
    Signal_PollTimerExpired = SimpleFsmSignal_UserStart,
    Signal_EnterDefrosting
};

static st_CoreCallbackTimer st_RunningTimer;
static SimpleFsm_t st_RunningFsm;
static RunningState_t runningState;
static uint16_t u16_FridgeRuntimeStart;
static uint16_t u16_FridgeRuntimeMinute;
static bool b_PowerOnDelayRefVarCooling = true;
static uint16_t u16_DeforstExitTemp = U16_DEFROST_EXIT_TEMPERATURE;
static EnterDefrostingState_t enterDefrostingState;
static EnterDefrostingState_t enterDefrostingStateBak;
static bool b_FirstDefrostingEntered;
static CoolingEntryMode_t coolingEntryMode;
static SaveDefrostType_st st_SaveDefrostType;
static TurboFreezeDefrostingState_t turboFreezeDefrostingState;
static uint16_t u16_EnergyModeDefrostTimeStart;
static uint16_t u16_EnergyDefrostModeMinute;
static bool b_TurboFreezeDefrostingAutoExitState;
static bool b_OverLoadDefrostingInTurboFreeze;
static bool b_EnergyConsumptionMode;//能耗模式
static bool b_EnergyModeFirstDeforst;
static uint16_t u16_EnterEnergyModeTimeStart;
static uint16_t u16_EnterEnergyModeMinute;
//static uint16_t u16_ExitEnergyModeTimeStart;
//static uint16_t u16_ExitEnergyModeMinute;
static uint16_t u16_ExitEnergyModeTimeStartRt;
static uint16_t u16_ExitEnergyModeTimeStartRs;
static uint16_t u16_ExitEnergyModeTimeStartHum;
static uint16_t u16_RoomTempBackup;
static uint16_t u16_EneryExitRoomTempBackup;
static bool b_EneryExitRoomTempSwingsOverRange;

static bool b_CondensationMode;//凝露模式
static uint16_t u16_EnterCondensationModeTimeStart;
static uint16_t u16_EnterCondensationModeMinute;
static uint16_t u16_ExitCondensationModeTimeStart;
static uint16_t u16_ExitCondensationModeMinute;
static uint8_t u8_RefDoorCounter;
static uint8_t u8_FrzDoorCounter;
static uint32_t u32_RefFrzDoorCounter;

bool b_RefFrostReduceMode;  //减霜模式
static bool b_ValveStayRefFrost;
static bool b_ForceRefFrostReduceMode;  //强制减霜模式

static bool b_IonGeneratorForcedCtrl;	//离子发生器强制控制
static uint16_t u16_IonGeneratorForcedCtrlTimeStart;
static uint16_t u16_IonGeneratorForcedCtrlMinute;

static uint16_t u16_ExitRefFrostReduceModeTemp;//减霜模式 - X退出温度
static uint8_t u8_RefFrostDoorCounterTemp;
static uint8_t u8_FrzFrostDoorCounterTemp;
static uint32_t u32_RefFrostDoorCounter;

//static uint8_t u8_RefFrostDoorCounter;
static uint16_t u16_RefFrostStartMinute;
static uint16_t u16_ValveStayRefFrostStartMinute;
static uint16_t u16_OverCoolingProtectStartSecond;

static bool b_CompOnDefrostOverLoad;
static uint16_t u16_TotalPower;
static uint32_t u32_ElectricEnergy;

static void RunningState_CoolingCycle(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);
static void RunningState_Defrosting(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data);

static void Judge_EnergyConsumptionMode(void)
{
    bool door_state = Get_DoorSwitchState((DoorTypeId_t)DOOR_ALL);
    UserMode_t user_mode = Get_UserMode();
    uint8_t fault_number = Get_FaultCodeNumber();
    HumidityRange_t humi_range = Get_HumidityRange();
    RoomTempRange_t room_range = Get_RoomTempRange();
    bool b_room_energy = false;
    uint16_t room_temp = Get_SensorValue((uint8_t)SENSOR_ROOM);
    bool b_RoomTempSwingsOverRange = false;
    uint8_t ref_door_counter = 0;
    bool b_time_out = false;
    uint8_t frz_temp_set = Get_FrzSetTemp();

    if((room_range == RT_BELOW18) || (room_range == RT_BELOW35))
    {
        b_room_energy = true;
    }

    if((u16_RoomTempBackup > (room_temp + U16_TEMP_SWINGS_RANGE)) ||
        ((u16_RoomTempBackup + U16_TEMP_SWINGS_RANGE) < room_temp))
    {
        u16_RoomTempBackup = room_temp;
        b_RoomTempSwingsOverRange = true;
    }

    if(false == b_EnergyConsumptionMode)
    {
        if((false == door_state) &&
            (true == b_room_energy) &&
            (false == b_RoomTempSwingsOverRange) &&
            ((UserMode_t)eManual_Mode == user_mode) &&
            (frz_temp_set >= FRZ_LEVEL_F22) &&
            (0 == fault_number) &&
            (HBELOW70 >= humi_range))
        {
            u16_EnterEnergyModeMinute = Get_MinuteElapsedTime(u16_EnterEnergyModeTimeStart);
            if(u16_EnterEnergyModeMinute >= U16_ENTER_ENERGY_MODE_MINUTES)
            {
                b_EnergyConsumptionMode = true;
                b_EnergyModeFirstDeforst = false;
                Clear_EnergyModeDeforstCounter();
                u16_RoomTempBackup = room_temp;
                u16_EneryExitRoomTempBackup = room_temp;
                u16_ExitEnergyModeTimeStartHum = Get_MinuteCount();
                u16_ExitEnergyModeTimeStartRt = Get_MinuteCount();
                u16_ExitEnergyModeTimeStartRs = Get_MinuteCount();
                u16_EnergyModeDefrostTimeStart = Get_MinuteCount();
                u8_RefDoorCounter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_REF);
                u8_FrzDoorCounter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_FRZ);
                u32_RefFrzDoorCounter = 0;
            }
        }
        else
        {
            u16_EnterEnergyModeTimeStart = Get_MinuteCount();
        }
    }
    else
    {
        ref_door_counter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_REF);
        u32_RefFrzDoorCounter += (uint8_t)(ref_door_counter - u8_RefDoorCounter);
        u8_RefDoorCounter = ref_door_counter;
        ref_door_counter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_FRZ);
        u32_RefFrzDoorCounter += (uint8_t)(ref_door_counter - u8_FrzDoorCounter);
        u8_FrzDoorCounter = ref_door_counter;

        if((u16_EneryExitRoomTempBackup > (room_temp + U16_TEMP_SWINGS_RANGE)) ||
            ((u16_EneryExitRoomTempBackup + U16_TEMP_SWINGS_RANGE) < room_temp))
        {
            b_EneryExitRoomTempSwingsOverRange = true;
        }
        else
        {
            b_EneryExitRoomTempSwingsOverRange = false;
        }

        if((false == b_room_energy) &&
            (U16_ROOM_RANGE_EXIT_MINUTES < Get_MinuteElapsedTime(u16_ExitEnergyModeTimeStartRt)))
        {
            b_time_out = true;
        }
        else if(true == b_room_energy)
        {
            u16_ExitEnergyModeTimeStartRt = Get_MinuteCount();
        }

        if((true == b_EneryExitRoomTempSwingsOverRange) &&
            (U16_ROOM_SWINGS_EXIT_MINUTES < Get_MinuteElapsedTime(u16_ExitEnergyModeTimeStartRs)))
        {
            b_time_out = true;
        }
        else if(false == b_EneryExitRoomTempSwingsOverRange)
        {
            u16_ExitEnergyModeTimeStartRs = Get_MinuteCount();
        }

        if((HBELOW70 < humi_range) &&
            (U16_HUMI_RANGE_EXIT_MINUTES < Get_MinuteElapsedTime(u16_ExitEnergyModeTimeStartHum)))
        {
            b_time_out = true;
        }
        else if(HBELOW70 >= humi_range)
        {
            u16_ExitEnergyModeTimeStartHum = Get_MinuteCount();
        }

        if((u32_RefFrzDoorCounter > U8_ENERGY_MODE_EXIT_DOOR_OPEN_COUNT) ||
            //((EnterDefrostingState_t)eEnterState_OverLoadError == enterDefrostingState) ||
            ((UserMode_t)eManual_Mode != user_mode) ||
            ((UserMode_t)eManual_Mode == user_mode && frz_temp_set < FRZ_LEVEL_F22) ||
            (0 != fault_number) ||
            (true == b_time_out))
        {
            b_EnergyConsumptionMode = false;
            u16_RoomTempBackup = room_temp;
            Clear_EnergyModeDeforstCounter();
            u16_EnterEnergyModeTimeStart = Get_MinuteCount();
        }
    }
}


//GGS - 13.9 
static void Judge_CondensationMode(void)
{
    HumidityRange_t humi_range = Get_HumidityRange();
    RoomTempRange_t room_range = Get_RoomTempRange();
    bool b_room_condensation = false;
    bool b_turbo_cool = false;
    bool b_TurboFreeze = false;

    b_turbo_cool = Get_TurboCoolState();
    b_TurboFreeze = Get_TurboFreezeState();
    //250523 - 
    if((room_range == RT_BELOW28) || (room_range == RT_BELOW35))
    {
        b_room_condensation = true;
    }

    if(false == b_CondensationMode)
    {
        if(
            (true == b_room_condensation) 
            && (HBELOW70 < humi_range) 
            && (false == b_turbo_cool) 
            //&& (false == b_TurboFreeze)//
            )
        {
            u16_EnterCondensationModeMinute = Get_MinuteElapsedTime(u16_EnterCondensationModeTimeStart);
            if(u16_EnterCondensationModeMinute >= U16_ENTER_CONDENSATION_MODE_MINUTES)
            {
                b_CondensationMode = true;
                u16_ExitCondensationModeTimeStart = Get_MinuteCount();
            }
        }
        else
        {
            u16_EnterCondensationModeTimeStart = Get_MinuteCount();
        }
    }
    else
    {
        if(true == b_turbo_cool)//速冷退出凝露模式
        {
            b_CondensationMode = false;
            u16_EnterCondensationModeTimeStart = Get_MinuteCount();
        }
        else if(
            (false == b_room_condensation) 
            || (HBELOW70 >= humi_range) 
            //|| (true == b_turbo_cool) 
            //|| (true == b_TurboFreeze)//
            )
        {
            u16_ExitCondensationModeMinute = Get_MinuteElapsedTime(u16_ExitCondensationModeTimeStart);
            if(u16_ExitCondensationModeMinute >= U16_EXIT_CONDENSATION_MODE_MINUTES)
            {
                b_CondensationMode = false;
                u16_EnterCondensationModeTimeStart = Get_MinuteCount();
            }
        }
        else
        {
            u16_ExitCondensationModeTimeStart = Get_MinuteCount();
        }
    }
}

static void Judge_RefFrostReduceMode(void)
{
    RoomTempRange_t room_range= Get_RoomTempRange();
    RoomTempRangeET_t room_range_ET = Get_RoomTempRangeET();
    uint8_t comp_on_valve_still_on_ref_time;
    uint8_t ref_temp_set = Get_RefSetTemp();
    uint8_t door_openclose_count;
    bool is_ref_sensor_error = Get_SensorError(SENSOR_REF);
    bool is_ref_deforst_sensor_error = Get_SensorError(SENSOR_REF_DEFROST);
    uint16_t ref_temp = Get_SensorValue(SENSOR_REF);
    uint16_t ref_deforst_temp = Get_SensorValue(SENSOR_REF_DEFROST);
    uint16_t exit_temp;
    CoolingCompState_t comp_state = Get_CoolingCompState();

    door_openclose_count = Get_DoorOpenCloseCounter(DOOR_REF);
    u32_RefFrostDoorCounter += (uint8_t)(door_openclose_count - u8_RefFrostDoorCounterTemp);
    u8_RefFrostDoorCounterTemp = door_openclose_count;
    door_openclose_count = Get_DoorOpenCloseCounter(DOOR_FRZ);
    u32_RefFrostDoorCounter += (uint8_t)(door_openclose_count - u8_FrzFrostDoorCounterTemp);
    u8_FrzFrostDoorCounterTemp = door_openclose_count;

    if(eRunning_CoolingCycle == runningState &&
        eCooling_CompOn == comp_state &&
        b_RefFrostReduceMode == false)
    {
        if(
            (is_ref_deforst_sensor_error == false) 
            &&(room_range > RT_BELOW13) 
            &&(ref_deforst_temp < CON_F23P0_DEGREE)
            )
        {
            if(Get_SecondElapsedTime(u16_OverCoolingProtectStartSecond) > 
               U16_REF_FROST_OVERCOOLING_PROTECT_TIME_SECONDS)
            {
                Set_RefFrostReduceMode();
            }
        }
        else
        {
            u16_OverCoolingProtectStartSecond = Get_SecondCount();
        }

        //GGS - 10.6 (2)
        comp_on_valve_still_on_ref_time = ary_RefReduceValveOnRefTime[room_range];
        if(Driver_ValveStateGetStayTime(Valve_FrzOFF_RefON) >= comp_on_valve_still_on_ref_time)
        {
            if(
                (is_ref_sensor_error == false) 
                &&(ref_temp > CON_15P0_DEGREE) 
                &&(is_ref_deforst_sensor_error == false) 
                &&(ref_deforst_temp < CON_F20P0_DEGREE)
                )
            {
                u16_ExitRefFrostReduceModeTemp = CON_8P0_DEGREE;
            }
            else if(u32_RefFrostDoorCounter > U8_REF_FROST_REDUCE_DOOR_OPEN_COUNT)
            {
                u16_ExitRefFrostReduceModeTemp = CON_6P0_DEGREE;
            }
            else
            {
                exit_temp = 10 * (ref_temp_set - 1) + CON_0P0_DEGREE - 20;

                //if(IsRoomTempBelowEightDegree())
                if(room_range_ET == RT2_BELOW8)
                {
                    u16_ExitRefFrostReduceModeTemp = CON_3P0_DEGREE;
                }
                else
                {
                    u16_ExitRefFrostReduceModeTemp = MAX(exit_temp, CON_3P0_DEGREE);
                }
            }
            u16_RefFrostStartMinute = Get_MinuteCount();
            u16_ValveStayRefFrostStartMinute = Get_MinuteCount();
            b_ValveStayRefFrost = true;
            b_RefFrostReduceMode = true;
        }
    }
    else
    {
        u16_OverCoolingProtectStartSecond = Get_SecondCount();
    }

    if(b_RefFrostReduceMode)
    {
        Vote_DeviceStatus((uint8_t)FSM_RefFrostReduceControl, DEVICE_Valve, Valve_FrzON_RefOFF);
        Vote_DeviceStatus((uint8_t)FSM_RefFrostReduceControl, DEVICE_RefFan, U8_REF_FROST_REDUCE_REF_FAN_DUTY);
        Vote_DeviceStatus((uint8_t)FSM_RefFrostReduceControl, DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLOPEN);
        Vote_DeviceStatus((uint8_t)FSM_RefFrostReduceControl, DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLCLOSE);
        if(
            (b_ForceRefFrostReduceMode == false)
            &&(b_EnergyConsumptionMode == true)  
            &&(Get_MinuteElapsedTime(u16_RefFrostStartMinute) >
            U8_REF_FROST_REDUCE_RUN_IN_ENERGY_MODE_MINUTES)
            )
        {
            Exit_RefFrostReduceMode();
        }
        else
        {
            if((b_EnergyConsumptionMode == true) && (b_ForceRefFrostReduceMode == false))
            {
                u16_ExitRefFrostReduceModeTemp = U16_ENERGY_MODE_DEFROST_EXIT_TEMPERATURE;
            }

            if((is_ref_deforst_sensor_error == false) && (ref_deforst_temp >= u16_ExitRefFrostReduceModeTemp))
            {
                b_ForceRefFrostReduceMode = false;
                Exit_RefFrostReduceMode();
            }
            else if((is_ref_deforst_sensor_error == true) && Get_RefFrostReduceTempOn())
            {
                b_ForceRefFrostReduceMode = false;
                Exit_RefFrostReduceMode();
            }
        }
    }
    //低温条件变温要求开机切阀，不执行减霜，制冷请求立即执行。
    if(Get_RefVarCompOnValveOnRequireState())
    {
        Exit_RefFrostReduceMode();
    }

    if(b_ValveStayRefFrost == true && 
        Get_MinuteElapsedTime(u16_ValveStayRefFrostStartMinute) >
        U8_VALVE_STAY_REF_FROST_MINUTES)
    {
        b_ValveStayRefFrost = false;
    }
}

static void Ctrl_IonGenerator(void)
{
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_Off);

    if(b_EnergyConsumptionMode == false && eMode_FridgePowerOn != coolingEntryMode)
    {
        if(Get_FanParameter(REF_FAN) > 0 && Is_FanError(REF_FAN) == FALSE)
        {
            if(Get_FanTotalRunMinutes(REF_FAN) > U16_REF_FAN_ION_ENABLE_TOTAL_MINUTES)
            {
                Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_On);
                Clear_FanTotalRunMinutes(REF_FAN);
                Clear_IonGeneratorOnMinutes();
            }
            else if(Get_IonGeneratorOnMinutes() < U16_ION_ENABLE_TOTAL_MINUTES)
            {
                Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_On);
            }
        }
    }

    //强制开启
    if(b_IonGeneratorForcedCtrl)
    {
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_On);

        u16_IonGeneratorForcedCtrlMinute = Get_MinuteElapsedTime(u16_IonGeneratorForcedCtrlTimeStart);
        if(u16_IonGeneratorForcedCtrlMinute >= U16_ION_GENERATOR_FORCE_CTRL_MINUTES)
        {
            Clear_ForceIonGeneratorWorkState();
            b_IonGeneratorForcedCtrl = false;
            u16_IonGeneratorForcedCtrlTimeStart = Get_MinuteCount();
        }
    }
    else
    {
        u16_IonGeneratorForcedCtrlTimeStart = Get_MinuteCount();
    }
}

static void Update_PowerOnDelayRefVarCooling(void)
{
    if((CoolingEntryMode_t)eMode_FridgePowerOn == coolingEntryMode)
    {
        if((true == b_PowerOnDelayRefVarCooling) && (u16_FridgeRuntimeMinute >= U16_POWER_ON_DELAY_REF_VAR_COLLING_MINUTES))
        {
            Reset_DoubleDamper();
            b_PowerOnDelayRefVarCooling = false;
        }
    }
    else
    {
        b_PowerOnDelayRefVarCooling = false;
    }
}

void Update_ElectricEnergy(void)
{
    //BC
    uint16_t u16_12V_power = Get_12VPower();
    uint16_t comp_power = Get_CompPower();
    uint16_t voltage = 0;
    float voltage_coef = 0;
    uint16_t defrost_heater_power = 0;
    uint16_t offset = U16_POWER_OFFSET;
    uint8_t b_defrost_heater_on = Get_ResolvedDeviceStatus(DEVICE_FrzDefHeater);

    if(DS_On == b_defrost_heater_on)
    {
        voltage = Get_CompBusVoltage();
        voltage_coef = ((float)voltage) / FLOAT_AC_VOLTAGE;
        voltage_coef *= voltage_coef;
        defrost_heater_power = (uint16_t)(voltage_coef * FLOAT_DEFROST_RATING);
    }

    if(Get_CompFeedbackFreq() > 0)
    {
        offset = U16_COMP_POWER_OFFSET;
    }
    u16_TotalPower = defrost_heater_power + u16_12V_power + offset + comp_power / 2;
    u32_ElectricEnergy += (uint32_t)u16_TotalPower;
    
}

static void PollTimerExpired(void)
{
    u16_FridgeRuntimeMinute = Get_MinuteElapsedTime(u16_FridgeRuntimeStart);
    Update_PowerOnDelayRefVarCooling();
    Ctrl_VerticalBeamHeater();
    Process_UserMode();
    Judge_EnergyConsumptionMode();
    Judge_CondensationMode();
    Judge_RefFrostReduceMode();
    Ctrl_IonGenerator();
    SimpleFsm_SendSignal(&st_RunningFsm, Signal_PollTimerExpired, NULL);
}

static void ArmPollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_RunningTimer,
        PollTimerExpired,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_RunningTimer);
}


static void Save_DefrostType(EnterDefrostingState_t state)
{
    uint8_t u8_index = 0;

    st_SaveDefrostType.u8_SavedFlag = 0xAA;

    if(st_SaveDefrostType.u8_SavedCount < U8_DEFROST_TYPE_MAX_SAVE_NUMBER)
    {
        st_SaveDefrostType.u8_SavedCount++;
    }
    else
    {
        st_SaveDefrostType.u8_SavedCount = U8_DEFROST_TYPE_MAX_SAVE_NUMBER;
    }

    if(st_SaveDefrostType.u8_SavedCount >= 2)
    {
        for(u8_index = st_SaveDefrostType.u8_SavedCount - 1; u8_index > 0; u8_index--)
        {
            st_SaveDefrostType.ary_DefrostTypeBuff[u8_index] = st_SaveDefrostType.ary_DefrostTypeBuff[u8_index - 1];
        }
    }

    st_SaveDefrostType.ary_DefrostTypeBuff[0] = state;
}

static uint16_t Calc_DefrostExitTemp(void)
{
    uint8_t u8_defrost_condition_romm_temp_index = 0; // 正常化霜环温
    uint8_t u8_defrost_condition_number = 0; // 环温段内条件数量
    uint8_t u8_index = 0; // 查询循环索引
    uint16_t u16_ref_door_total_open_time_seconds = Get_DoorOpenTimeSecond((DoorTypeId_t)DOOR_REF); // 冷藏门开门时间
    uint16_t u16_frz_door_total_open_time_seconds = Get_DoorOpenTimeSecond((DoorTypeId_t)DOOR_FRZ);
    uint16_t u16_condition_ref_door_total_open_time_seconds = 0;
    uint16_t u16_condition_frz_door_total_open_time_seconds = 0;
    DefrostNormalCondition_st *p_condition = (DefrostNormalCondition_st *)NULL;
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint8_t fridge_type = Get_FridgeType();

    if(room_range <= RT_BELOW13)
    {
        u8_defrost_condition_romm_temp_index = 0;
    }
    else if(room_range <= RT_BELOW35)
    {
        u8_defrost_condition_romm_temp_index = 1;
    }
    else if(room_range <= RT_BELOW40)
    {
        u8_defrost_condition_romm_temp_index = 2;
    }
    else
    {
        u8_defrost_condition_romm_temp_index = 3;
    }

    p_condition = &ary_DefrostNormalCondition[u8_defrost_condition_romm_temp_index];

    u8_defrost_condition_number = p_condition->u8_DefrostConditionNumber;

    if(u8_defrost_condition_number > 0)
    {
        for(u8_index = 0; u8_index < u8_defrost_condition_number; u8_index++)
        {
            u16_condition_ref_door_total_open_time_seconds =
                p_condition->ary_DefrostCondition[u8_index].u16_RefDoorTotalOpenTimeSecond;

            u16_condition_frz_door_total_open_time_seconds =
                p_condition->ary_DefrostCondition[u8_index].u16_FrzVarDoorTotalOpenTimeSecond;

            if(((u16_ref_door_total_open_time_seconds >= u16_condition_ref_door_total_open_time_seconds) ||
                    (u16_frz_door_total_open_time_seconds >= u16_condition_frz_door_total_open_time_seconds)))
            {
                return p_condition->ary_DefrostCondition[u8_index].u16_DefrostExitTemp;
            }
        }
    }
    return U16_DEFROST_EXIT_TEMPERATURE;
}

static uint16_t Calc_DefrostExitTempFixedValue(void)
{
    return U16_DEFROST_EXIT_TEMPERATURE;
}

static void Judge_EnterFirstDefrost(void)
{
    if(u16_FridgeRuntimeMinute >= U16_FIRST_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_First;
        u16_DeforstExitTemp = Calc_DefrostExitTempFixedValue();
    }
    else
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
}

static bool Get_OverLoadDefrost(void)
{
    uint16_t comp_still_on_minute = Get_CompStillOnTimeMinute();
    bool b_ref_over_load = false;
    bool b_frz_over_load = false;

    if(b_CompOnDefrostOverLoad == false &&
        (coolingEntryMode == (CoolingEntryMode_t)eMode_DefrostingCompleted))
    {
        b_ref_over_load = Get_RefTempOverLoadDefrost();
        b_frz_over_load = Get_FrzTempOverLoadDefrost();

        if(comp_still_on_minute >= U16_OVERLOAD_DEFROST_COMP_STILL_ON_TIME_MINUTES &&
            (b_ref_over_load || b_frz_over_load))
        {
            b_CompOnDefrostOverLoad = true;
        }
        else if(comp_still_on_minute >= U16_OVERLOAD_DEFROST_COMP_STILL_LONG_ON_TIME_MINUTES &&
            Get_RefTempOverOnDefrost())
        {
            b_CompOnDefrostOverLoad = true;
        }
    }
    return b_CompOnDefrostOverLoad;
}

void Set_TurboFreezeDefrostingAutoExitState(bool state)
{
    b_TurboFreezeDefrostingAutoExitState = state;
    if(false == state)
    {
        turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None;
    }
    b_OverLoadDefrostingInTurboFreeze = false;
}

static void Judge_EnterTurboFreezeDefrost(void)
{
    bool b_TurboFreeze = Get_TurboFreezeState();
    uint16_t turbo_freeze_minute = Get_TurboFreezeTimeMinute();

    switch(turboFreezeDefrostingState)
    {
        case(TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None:
            if(true == b_TurboFreeze)
            {
                turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_First;
            }
            enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
            break;
        case(TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_First:
            if((true == b_TurboFreeze) &&
                (turbo_freeze_minute >= U16_TURBO_FRZ_DEFROST_COMP_TOTAL_ON_TIME_MINUTES))
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_TurboFreeze;
                u16_DeforstExitTemp = Calc_DefrostExitTempFixedValue();
                turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_Second;
            }
            else
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
            }
            break;
        case(TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_Second:
            if((false == b_TurboFreeze) && (true == b_TurboFreezeDefrostingAutoExitState))
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_TurboFreeze;
                u16_DeforstExitTemp = Calc_DefrostExitTemp();
                turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None;
            }
            else
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
            }
            break;
        default:
            break;
    }
}

static bool Get_SensorErrorDefrost(void)
{
    bool b_state = false;
    bool b_frz_def_snr_error = Get_SensorError((SensorType_t)SENSOR_DEFROST);
    bool b_frz_snr_error = Get_SensorError((SensorType_t)SENSOR_FRZ);
    bool b_ref_door_error = Get_DoorSwitchErrorState((DoorTypeId_t)DOOR_REF);
    bool b_frz_door_error = Get_DoorSwitchErrorState((DoorTypeId_t)DOOR_FRZ);

    if((true == b_frz_snr_error) || (true == b_frz_def_snr_error))
    {
        b_state = true;
    }

    if((true == b_ref_door_error) || (true == b_frz_door_error))
    {
        b_state = true;
    }

    return (b_state);
}

static void Judge_EnterSensorErrorDefrost(void)
{
    if(u16_FridgeRuntimeMinute >= U16_DEF_FUNCTION_ERROR_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_SensorError;
        u16_DeforstExitTemp = Calc_DefrostExitTempFixedValue();
    }
    else
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
}

static void Judge_EnterDefFunctionErrorDefrost(void)
{
    if(u16_FridgeRuntimeMinute >= U16_DEF_FUNCTION_ERROR_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_DefFunctionError;
        u16_DeforstExitTemp = Calc_DefrostExitTempFixedValue();
    }
    else
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
}

static void Judge_EnterEnergyModeDefrost(void)
{
    uint16_t energy_deforst_minute = 0;
    //uint8_t EnergyModeDeforstCounter = Get_EnergyModeDeforstCounter();
    uint8_t LongOrShortDeforstCounter = Get_LongOrShortDeforstCounter();

    if(false == b_EnergyModeFirstDeforst)
    {
        energy_deforst_minute = U16_ENERGY_FIRST_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES;
    }
    else
    {
        energy_deforst_minute = U16_ENERGY_MORMAL_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES;
    }

    u16_EnergyDefrostModeMinute = Get_MinuteElapsedTime(u16_EnergyModeDefrostTimeStart);
    if(u16_EnergyDefrostModeMinute >= energy_deforst_minute)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_EnergyMode;
        /*
        if(EnergyModeDeforstCounter >= U8_ENERGY_MODE_DEFROST_COUNT) // 2025-06-03
        {
            u16_DeforstExitTemp = U16_ENERGY_MODE_DEFROST_EXIT_TEMPERATURE_LONG; //ENERGY MODE LONG DEFROST
        }
        else
        {
            u16_DeforstExitTemp = U16_ENERGY_MODE_DEFROST_EXIT_TEMPERATURE; // ENERGY MODE SHORT DEFROST
        }
        */

        if(LongOrShortDeforstCounter >= U8_LONG_OR_SHORT_DEFROST_COUNT) // 2025-06-03
        {
            u16_DeforstExitTemp = U16_DEFROST_EXIT_TEMPERATURE_LONG; //ENERGY MODE LONG DEFROST
        }
        else
        {
            u16_DeforstExitTemp = U16_DEFROST_EXIT_TEMPERATURE_SHORT; // ENERGY MODE SHORT DEFROST
        }
        
        u16_EnergyModeDefrostTimeStart = Get_MinuteCount();
        b_EnergyModeFirstDeforst = true;
    }
    else
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
}

static void Judge_EnterNormalDefrost(void)
{
    uint8_t u8_defrost_condition_romm_temp_index = 0; // 正常化霜环温
    uint8_t u8_defrost_condition_number = 0; // 环温段内条件数量
    uint8_t u8_index = 0; // 查询循环索引
    uint8_t u8_item = 0; // 查询条件索引
    uint16_t u16_ref_door_total_open_time_seconds = Get_DoorOpenTimeSecond((DoorTypeId_t)DOOR_REF); // 冷藏门开门时间
    uint16_t u16_frz_door_total_open_time_seconds = Get_DoorOpenTimeSecond((DoorTypeId_t)DOOR_FRZ);
    uint16_t u16_condition_fridge_total_on_time_minutes = 0;
    uint16_t u16_condition_ref_door_total_open_time_seconds = 0;
    uint16_t u16_condition_frz_door_total_open_time_seconds = 0;
    DefrostNormalCondition_st *p_condition = (DefrostNormalCondition_st *)NULL;
    RoomTempRange_t room_range = Get_RoomTempRange();
    uint8_t fridge_type = Get_FridgeType();
    //uint8_t NormalModeDeforstCounter = Get_NormalModeDeforstCounter();
    uint8_t LongOrShortDeforstCounter = Get_LongOrShortDeforstCounter();

    enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;

    if(room_range <= RT_BELOW13)
    {
        u8_defrost_condition_romm_temp_index = 0;
    }
    else if(room_range <= RT_BELOW35)
    {
        u8_defrost_condition_romm_temp_index = 1;
    }
    else if(room_range <= RT_BELOW40)
    {
        u8_defrost_condition_romm_temp_index = 2;
    }
    else
    {
        u8_defrost_condition_romm_temp_index = 3;
    }

    p_condition = &ary_DefrostNormalCondition[u8_defrost_condition_romm_temp_index];

    u8_defrost_condition_number = p_condition->u8_DefrostConditionNumber;

    if(u8_defrost_condition_number > 0)
    {
        for(u8_index = 0, u8_item = u8_defrost_condition_number - 1;
            u8_index < u8_defrost_condition_number;
            u8_index++, u8_item--)
        {
            u16_condition_fridge_total_on_time_minutes =
                p_condition->ary_DefrostCondition[u8_item].u16_FridgeTotalOnTimeMinutes;

            u16_condition_ref_door_total_open_time_seconds =
                p_condition->ary_DefrostCondition[u8_item].u16_RefDoorTotalOpenTimeSecond;

            u16_condition_frz_door_total_open_time_seconds =
                p_condition->ary_DefrostCondition[u8_item].u16_FrzVarDoorTotalOpenTimeSecond;

            if((u16_FridgeRuntimeMinute >= u16_condition_fridge_total_on_time_minutes) &&
                ((u16_ref_door_total_open_time_seconds >= u16_condition_ref_door_total_open_time_seconds) ||
                    (u16_frz_door_total_open_time_seconds >= u16_condition_frz_door_total_open_time_seconds)))
            {
                enterDefrostingState = (EnterDefrostingState_t)eEnterState_Normal;
                u16_DeforstExitTemp = p_condition->ary_DefrostCondition[u8_item].u16_DefrostExitTemp;
                //4次短后，长化霜温度赋值 
                if(LongOrShortDeforstCounter >= U8_LONG_OR_SHORT_DEFROST_COUNT) 
                {
                    u16_DeforstExitTemp = U16_DEFROST_EXIT_TEMPERATURE_LONG; // NORMAL MODE LONG DEFROST
                }

                break;
            }
        }
    }
    /*
    if(NormalModeDeforstCounter >= U8_NORMAL_MODE_DEFROST_COUNT) // 2025-06-03
    {
        u16_DeforstExitTemp = U16_NORMAL_MODE_DEFROST_EXIT_TEMPERATURE_LONG; // NORMAL MODE LONG DEFROST
    }
    */
}

static void Update_EnterDefrostingState(void)
{
    bool b_SensorErrorDefrost = false;
    bool b_DefFunctionErrorDefrost = false;
    bool b_turbo_cool = false;
    bool b_TurboFreeze = false;
    bool b_OverLoadDefrost = false;

    b_SensorErrorDefrost = Get_SensorErrorDefrost();
    b_DefFunctionErrorDefrost = Get_DefrostFunctionError();
    b_turbo_cool = Get_TurboCoolState();
    b_TurboFreeze = Get_TurboFreezeState();
    b_OverLoadDefrost = Get_OverLoadDefrost();

    if(false == b_FirstDefrostingEntered)
    {
        Judge_EnterFirstDefrost();
    }
    else if(b_OverLoadDefrost == true &&
        u16_FridgeRuntimeMinute >= U16_OVERLOAD_DEFROST_FRIDGE_TOTAL_ON_TIME_MINUTES)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_OverLoadError;
        u16_DeforstExitTemp = Calc_DefrostExitTempFixedValue();
        if(b_TurboFreeze)
        {
            b_OverLoadDefrostingInTurboFreeze = true;
        }
    }
    else if(b_OverLoadDefrostingInTurboFreeze == false &&
        ((true == b_TurboFreeze) || (turboFreezeDefrostingState != (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None)))
    {
        Judge_EnterTurboFreezeDefrost();
    }
    else if(true == b_SensorErrorDefrost)
    {
        Judge_EnterSensorErrorDefrost();
    }
    else if(true == b_DefFunctionErrorDefrost)
    {
        Judge_EnterDefFunctionErrorDefrost();
    }
    else if(true == b_turbo_cool)
    {
        enterDefrostingState = (EnterDefrostingState_t)eEnterState_Forbid;
    }
    else if(true == b_EnergyConsumptionMode)
    {
        Judge_EnterEnergyModeDefrost();
    }
    else
    {
        Judge_EnterNormalDefrost();
    }

    if(enterDefrostingStateBak != enterDefrostingState)
    {
        enterDefrostingStateBak = enterDefrostingState;
        Save_DefrostType(enterDefrostingState);
    }
}

static void RunningState_CoolingCycle(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    uint16_t u16_sensor_value;
    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            runningState = (RunningState_t)eRunning_CoolingCycle;
            CoolingCycle_Init(coolingEntryMode);
            Clear_DefrostMode();
            b_CompOnDefrostOverLoad = false;
            //Clear_RefFrostReduceModeDelayCoolingState();
            Set_FridgeType(GetMachineType());
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            u16_sensor_value = Get_SensorValue((SensorType_t)SENSOR_REF_DEFROST);
            if(u16_sensor_value <= CON_11P0_DEGREE)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_IonGenerator, DS_On);
            }
            else if(u16_sensor_value >= CON_12P0_DEGREE)
            {
                Vote_DeviceStatus(FSM_SpecialModes, DEVICE_IonGenerator, DS_Off);
            }
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            CoolingCycle_Exit();
            break;
        default:
            break;
    }
}

static void RunningState_Defrosting(SimpleFsm_t *fsm, SimpleFsmSignal_t signal, const void *data)
{
    DefrostMode_t defrost_mode;

    switch(signal)
    {
        case(SimpleFsmSignal_t)Signal_Entry:
            runningState = (RunningState_t)eRunning_Defrosting;
            Defrosting_Init(enterDefrostingState);
            b_FirstDefrostingEntered = true;
            b_ValveStayRefFrost = false;
            b_ForceRefFrostReduceMode = false;//解除强制
            //Clear_RefFrostReduceModeDelayCoolingState();
            Set_FridgeType(GetMachineType());
            break;
        case(SimpleFsmSignal_t)Signal_PollTimerExpired:
            defrost_mode = Get_DefrostMode();
            if((DefrostMode_t)eDefrostMode_Completed == defrost_mode)
            {
                Clear_ValveStayRefForstState();
                coolingEntryMode = (CoolingEntryMode_t)eMode_DefrostingCompleted;
                SimpleFsm_Transition(&st_RunningFsm, RunningState_CoolingCycle);
            }
            break;
        case(SimpleFsmSignal_t)Signal_Exit:
            Defrosting_Exit();
            break;
        default:
            break;
    }
}

void FridgeRunner_Init(void)
{
    if(IsValveAlreadyReset() == false)
    {
        Drive_ValveReset();
    }
    Update_PowerOnDelayRefVarCooling();
    SimpleFsm_Init(&st_RunningFsm, RunningState_CoolingCycle, NULL);
    ArmPollTimer(U16_FRIDGERUNNER_CYCLE_SECOND);
    b_FirstDefrostingEntered = false;
    u16_FridgeRuntimeStart = Get_MinuteCount();
    u16_FridgeRuntimeMinute = 0;
    turboFreezeDefrostingState = (TurboFreezeDefrostingState_t)eTurboFreezeDefrosting_None;
    b_RefFrostReduceMode = false;
    b_ValveStayRefFrost = false;
    u8_RefFrostDoorCounterTemp = Get_DoorOpenCloseCounter(DOOR_REF);
    u8_FrzFrostDoorCounterTemp = Get_DoorOpenCloseCounter(DOOR_FRZ);
    u32_RefFrostDoorCounter = 0;
}

void FridgeRunner_Exit(void)
{
    Stop_PollTimer();
    CoolingCycle_Exit();
    Defrosting_Exit();
    b_ForceRefFrostReduceMode = false;//解除强制
    Exit_RefFrostReduceMode();
}

RunningState_t Get_RunningState(void)
{
    return (runningState);
}

EnterDefrostingState_t Get_EnterDefrostingState(void)
{
    return (enterDefrostingState);
}

uint8_t Get_SavedDefrostType(uint8_t **p_defrost_type_buff)
{
    *p_defrost_type_buff = (uint8_t *)(st_SaveDefrostType.ary_DefrostTypeBuff);

    return (st_SaveDefrostType.u8_SavedCount);
}

void Clear_FridgeTotalOnTimeMinute(void)
{
    u16_FridgeRuntimeStart = Get_MinuteCount();
    u16_FridgeRuntimeMinute = 0;
}

uint16_t Get_FridgeTotalOnTimeMinute(void)
{
    return (u16_FridgeRuntimeMinute);
}

uint16_t Get_DeforstExitTemp(void)
{
    return (u16_DeforstExitTemp);
}

void Set_DeforstExitTemp(uint16_t u16_Temp)
{
    u16_DeforstExitTemp = u16_Temp;
}

void Set_CoolingEntryMode(CoolingEntryMode_t mode)
{
    coolingEntryMode = mode;
}

void Set_EnergyConsumptionModeState(bool state)
{
    b_EnergyConsumptionMode = state;
}

void Force_EnterEnergyConsumptionModeState(void)
{
    uint16_t room_temp = Get_SensorValue((uint8_t)SENSOR_ROOM);

    b_EnergyConsumptionMode = true;
    b_EnergyModeFirstDeforst = false;
    u16_RoomTempBackup = room_temp;
    u16_EneryExitRoomTempBackup = room_temp;
    u16_ExitEnergyModeTimeStartHum = Get_MinuteCount();
    u16_ExitEnergyModeTimeStartRt = Get_MinuteCount();
    u16_ExitEnergyModeTimeStartRs = Get_MinuteCount();
    u16_EnergyModeDefrostTimeStart = Get_MinuteCount();
    u8_RefDoorCounter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_REF);
    u8_FrzDoorCounter = Get_DoorOpenCloseCounter((DoorTypeId_t)DOOR_FRZ);
    u32_RefFrzDoorCounter = 0;
}

bool Get_EnergyConsumptionModeState(void)
{
    return b_EnergyConsumptionMode;
}

//获取凝露状态
bool Get_CondensationModeState(void)
{
    return b_CondensationMode;
}

bool Get_RefFrostReduceMode(void)
{
    return b_RefFrostReduceMode;
}

void Force_Set_RefFrostReduceMode(void)
{
    if(
        (eRunning_CoolingCycle == runningState)
        //&&(b_RefFrostReduceMode == false)
     )
    {
        b_ForceRefFrostReduceMode = true;
        u16_ExitRefFrostReduceModeTemp = CON_10P0_DEGREE;
        u16_RefFrostStartMinute = Get_MinuteCount();
        b_RefFrostReduceMode = true;
    }
}

void Set_RefFrostReduceMode(void)
{
    RoomTempRange_t room_range= Get_RoomTempRange();
    uint8_t ref_temp_set = Get_RefSetTemp();
    uint16_t exit_temp;

    if(
        (eRunning_CoolingCycle == runningState)
        && (b_RefFrostReduceMode == false)
    //    && (b_EnergyConsumptionMode == false)
        )
    {
        exit_temp = 10 * (ref_temp_set - 1) + CON_0P0_DEGREE - 20;
        if(room_range == RT_BELOW13)
        {
            u16_ExitRefFrostReduceModeTemp = CON_3P0_DEGREE;
        }
        else
        {
            u16_ExitRefFrostReduceModeTemp = MAX(exit_temp, CON_3P0_DEGREE);
        }
        u16_RefFrostStartMinute = Get_MinuteCount();
        b_RefFrostReduceMode = true;
    }
}

void Exit_RefFrostReduceMode(void)
{
    if(b_ForceRefFrostReduceMode)//强制减霜，需要等正常退出温度后才能解除
    {
        return;
    }
    b_RefFrostReduceMode = false;
    u8_RefFrostDoorCounterTemp = Get_DoorOpenCloseCounter(DOOR_REF);
    u8_FrzFrostDoorCounterTemp = Get_DoorOpenCloseCounter(DOOR_FRZ);
    u32_RefFrostDoorCounter = 0;
    Vote_DeviceStatus((uint8_t)FSM_RefFrostReduceControl, DEVICE_Valve, DS_DontCare);
    Vote_DeviceStatus((uint8_t)FSM_RefFrostReduceControl, DEVICE_RefFan, DS_DontCare);
    Vote_DeviceStatus((uint8_t)FSM_RefFrostReduceControl, DEVICE_RefDamper, DS_DontCare);
    Vote_DeviceStatus((uint8_t)FSM_RefFrostReduceControl, DEVICE_RefVarDamper, DS_DontCare);
}

bool Get_PowerOnDelayRefVarCoolingState(void)
{
    return (b_PowerOnDelayRefVarCooling);
}

bool Get_ValveStayRefForstState(void)
{
    return (b_ValveStayRefFrost);
}

void Clear_ValveStayRefForstState(void)
{
    b_ValveStayRefFrost = false;
}

void Clear_ForceRefFrostReduceModeState(void)
{
    b_ForceRefFrostReduceMode = false;
}

uint16_t Get_TotalPower(void)
{
    return u16_TotalPower;
}

uint32_t Get_ElectricEnergy(void)
{
    return u32_ElectricEnergy;
}

void Forced_IonGeneratorState(bool state)
{
    b_IonGeneratorForcedCtrl = state;
}