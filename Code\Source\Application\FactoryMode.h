/*!
 * @file
 * @brief This file defines public constants, types and functions for the factory mode.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef FACTORY_MODE_H
#define FACTORY_MODE_H

#include <stdint.h>
#include <stdbool.h>

#define FACTORY_DATA_BUFFER 480

typedef enum
{
    FACTORY_ITEM_VAL_UINT32 = 0x0,
    FACTORY_ITEM_VAL_INT32 = 0x1,
    FACTORY_ITEM_VAL_UINT8 = 0x2,
    FACTORY_ITEM_VAL_INT8 = 0x3,
    FACTORY_ITEM_VAL_UINT16 = 0x4,
    FACTORY_ITEM_VAL_INT16 = 0x5
} factory_item_val_e;

typedef struct
{
    char *key;
    factory_item_val_e type;
    void (*check_factory_item)(void *data);
} factory_item_st;

typedef enum
{
    FactoryDevice_FrzDefHeater = 0,
    FactoryDevice_VBHeater,
    FactoryDevice_RefLamp,
    FactoryDevice_FrzLamp,
    FactoryDevice_<PERSON>z<PERSON>an,
    FactoryDevice_CondFan,
    FactoryDevice_Damper,
    FactoryDevice_Valve,
    FactoryDevice_RefFan,
    FactoryDevice_Max
} FactoryDevice_ID;
typedef uint8_t FactoryDeviceId_t;

typedef struct
{
    uint16_t u16_HighPowerLimit;
    uint16_t u16_LowPowerLimit;
} DevicePower_st;

typedef struct
{
    uint16_t u16_DevicePower;
    bool b_DeviceError;
} FactoryDeviceState_st;

void FactoryMode_Init(void);
void FactoryMode_Exit(void);
bool Get_FctWifiLedState(void);
bool Get_WifiFactoryMode(void);
bool Get_FctWifiMatchError(void);
uint16_t Get_FactoryRoomTemp(void);
void Get_FctUploadData(char *result, uint16_t len);
extern void str_n_cat(char *pDst, int n_str, ...);
uint8_t Get_FactoryDeviceFaultByte(void);
uint16_t Get_FactoryTotalPower(void);
#endif

