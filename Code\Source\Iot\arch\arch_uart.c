//#include "cpu\inc\xmi_uart.h"

#include "arch_define.h"
//#include <stdint.h>
#include "arch_uart.h"
#include "string.h"
#include "Adpt_Usart.h"
//#include "MCUResgiterDefine.h"
#include "IotUsr.h"

typedef enum{
    MD_OK,
    MD_BUSY1
}MD_STATUS;

#define USART_CH2     2
#define USART2_QUEUE_SIZE 528
MD_STATUS g_UartA1TxEnd = MD_OK; /* UARTA1 transmission end */
TByte g_uart2_tx_queue[USART2_QUEUE_SIZE] = {0};
TWord g_uart2_tx_front = 0;
TWord g_uart2_tx_rear = 0;
TByte g_uart2_rx_queue[USART2_QUEUE_SIZE] = {0};
TWord g_uart2_rx_front = 0; 
TWord g_uart2_rx_rear = 0; 
TBool g_uart2_recv_done = FALSE;


TBool uart_rx_queue_is_empty(TByte ch)
{
    if(ch == USART_CH2)
    {
	    return ( g_uart2_rx_rear == g_uart2_rx_front );
    }
    return FALSE;
}

TBool uart_rx_queue_is_full(TByte ch)
{
    if(ch == USART_CH2)
	{
		return ( (g_uart2_rx_rear + 1 ) % USART2_QUEUE_SIZE == g_uart2_rx_front );
	}

	return FALSE;
}
TBool uart_tx_queue_is_empty(TByte ch)
{
    if(ch == USART_CH2)
	{
		return ( g_uart2_tx_rear == g_uart2_tx_front );
	}

	return FALSE;
}
TBool uart_tx_queue_is_full(TByte ch)
{
    if(ch == USART_CH2)
	{
		return ( (g_uart2_tx_rear + 1 ) % USART2_QUEUE_SIZE == g_uart2_tx_front );
	}
	return FALSE;
}

void USART_WIFI_IRQHandler(void)
{
    if(Uart_GetStatus(USART_WIFI, UartFE))
    {
        Uart_ClrStatus(USART_WIFI, UartFE);
    }

    if(Uart_GetStatus(USART_WIFI, UartPE))
    {
        Uart_ClrStatus(USART_WIFI, UartPE);
    }

    if(Uart_GetStatus(USART_WIFI, UartRC))
    {
        Uart_ClrStatus(USART_WIFI, UartRC);

       if (uart_rx_queue_is_full(USART_CH2))
       {
           g_uart2_rx_queue [g_uart2_rx_rear++] = Uart_ReceiveData(USART_WIFI);
           g_uart2_rx_rear %= USART2_QUEUE_SIZE;
           g_uart2_rx_front = g_uart2_rx_rear = 0;
           g_uart2_rx_queue[g_uart2_rx_front] = 0;
       }
       else
       {
           g_uart2_rx_queue [g_uart2_rx_rear++] = Uart_ReceiveData(USART_WIFI);
           g_uart2_rx_rear %= USART2_QUEUE_SIZE;
       }
    }

    if(Uart_GetStatus(USART_WIFI, UartTC))
    {
        Uart_ClrStatus(USART_WIFI, UartTC);

        if (uart_tx_queue_is_empty(USART_CH2) == TRUE)
        {
            g_UartA1TxEnd = MD_OK;
            //USART_ConfigInt(USART_WIFI, USART_INT_TXDE, DISABLE);
        }
        else
        {
            g_UartA1TxEnd = MD_BUSY1;
            Uart_SendDataIt(USART_WIFI, g_uart2_tx_queue[g_uart2_tx_front++]);
            g_uart2_tx_front %= USART2_QUEUE_SIZE;
            //APP_LOG("%c",TXBA1);
        }
    }
}

void Wifi_UARTA1_Send_Byte(TByte byte)
{
	//g_Sending_cmd[USART_CH2].timeout = BUS_TIMEOUT_TIME;

	if (uart_tx_queue_is_empty(USART_CH2) == FALSE)
	{
        /* Disable UARTA1 interrupt operation */
        //USART_ConfigInt(USART_WIFI, USART_INT_TXDE, DISABLE);
        Uart_SendDataIt(USART_WIFI,byte);
        g_UartA1TxEnd = MD_BUSY1;//zhl add
        /* Enable UARTA1 interrupt operation */
        //USART_ConfigInt(USART_WIFI, USART_INT_TXDE, ENABLE);
	}
	//APP_LOG("%c",byte);
}

TBool Wifi_Uart_Get_Send_Idel_Flag(void)
{
   if (g_UartA1TxEnd == MD_OK)
    {
        return TRUE;
    }
   return FALSE;
}

TByte uart_send_data_with_interrupt(TByte ch,TByte *send_buffer,TByte length)
{
	TByte i;
	if (ch == USART_CH2)
	{
		for (i = 0; i < length; i++)
		{
			g_uart2_tx_queue[g_uart2_tx_rear++] = *(send_buffer+i);
			g_uart2_tx_rear %= USART2_QUEUE_SIZE;
		}
		//if ((Wifi_Uart_Get_Send_Idel_Flag()== TRUE)&&(uart_tx_queue_is_full(ch) == FALSE)) //zhl
		if (Wifi_Uart_Get_Send_Idel_Flag()== TRUE)
		{
			Wifi_UARTA1_Send_Byte(g_uart2_tx_queue[g_uart2_tx_front]);
			g_uart2_tx_front++; //zhl
			g_uart2_tx_front %= USART2_QUEUE_SIZE;
			return length;
		}
		return length; //zhl
	}

	return 0;
}

TBool uart_receive_data_with_interrupt(TByte ch, TByte *recv_buf, TByte recv_len)
{
	if(recv_buf == NULL || recv_len == 0)
	{
		return FALSE;
	}
    //USART_ConfigInt(USART_WIFI, USART_INT_RXDNE, ENABLE);
	if(uart_rx_queue_is_empty(ch) == TRUE)
	{
		return FALSE;
	}
	else
	{
		if(ch == USART_CH2)
		{
			*recv_buf = g_uart2_rx_queue[g_uart2_rx_front++];
			g_uart2_rx_front %= USART2_QUEUE_SIZE;
        }
	}

	return TRUE;
}

int uart_init(UART_COMM_T uart)
{
#if 0
    /* only support UART_MIIO_COMM now */
    if( UART_MIIO_COMM != uart )
        return -1;
#endif
    
    //SC_USCI2_Init();
   // enableInterrupts();
    return 0;
}

int uart_send_byte(UART_COMM_T uart, const unsigned char u8data)
{

    return (int)uart_send_data_with_interrupt(USART_CH2, (TByte *)&u8data, 1);
    //return uart_write_txbuf(uart, u8data);

     //uint16_t i = 0;
    /* Write one byte to the transmit data register */
    // USART_SendData(USART_WIFI, u8data);

    // /* Loop until USARTz DAT register is empty */
    // while (USART_GetFlagStatus(USART_WIFI, USART_FLAG_TXDE) == RESET)
    // {
    //     if(i > 5000)
    //     {
    //         break;
    //     }
    //     i++;
    // }
	// return 1;
}

int uart_recv_byte(UART_COMM_T uart, unsigned char *p_u8data)
{
     //return uart_read_rxbuf(uart, p_u8data);
    
	if( uart_receive_data_with_interrupt(USART_CH2, (TByte *)p_u8data, 1) == TRUE )
		return 1;
	return 0;

//     uint16_t i = 0;
//    /* Store the received byte in RxBuffer */
//     while (USART_GetFlagStatus(USART_WIFI, USART_FLAG_RXDNE) == RESET)
//     {
//         if(i > 9000)
//         {
//             break;
//         }
//         i++;
//     }
//     /* Store the received byte in RxBuffer */
//     *p_u8data = (unsigned char)USART_ReceiveData(USART_WIFI);
//     return 1;


}


int uart_recv_buffer_is_empty(void)
{
	// if (USART_GetFlagStatus(USART_WIFI, USART_FLAG_RXDNE) == RESET)
	// {
	// 	  return 1;
	// }
	
	// return 0;
    return uart_rx_queue_is_empty(USART_CH2);
}

void APP_LOG(char *pbuf, ...)
{
    //log_printf(0, "%c\n", pbuf);
}

void APP_LOG_IOT(char *pbuf, ...)
{
    //log_printf(0, "%c\n", pbuf);
}


