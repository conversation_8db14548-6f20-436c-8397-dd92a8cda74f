/**
* <AUTHOR>
* @date    2019
* @par     Copyright (c):
*
*    Copyright 2019 MIoT,MI
*
*    Licensed under the Apache License, Version 2.0 (the "License");
*    you may not use this file except in compliance with the License.
*    You may obtain a copy of the License at
*
*        http://www.apache.org/licenses/LICENSE-2.0
*
*    Unless required by applicable law or agreed to in writing, software
*    distributed under the License is distributed on an "AS IS" BASIS,
*    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
*    See the License for the specific language governing permissions and
*    limitations under the License.
*/
#ifndef _MIIO_API_H_
#define _MIIO_API_H_

#include "miio_define.h"
#include "miio_uart.h"
#include "Iotlist.h"

typedef enum
{
    WIFI_CMD_NONE,
    WIFI_CMD_NET_STATE,
    WIFI_CMD_TIME,
    WIFI_CMD_MAC,
    WIFI_CMD_MODEL,
    WIFI_CMD_VERSION,
    WIFI_CMD_GETWIFI,
    WIFI_CMD_GETARCH,
    WIF<PERSON>_CMD_REBOOT,
    WIFI_CMD_RESTORE,
    WIFI_CMD_SETWIFI,
    WIFI_CMD_SETMCU_VERSION,
    WIFI_CMD_FACTORY,
    WIFI_CMD_SN,
    WIFI_CMD_GET_SN,
    WIFI_CMD_SET_MODEL,
    WIFI_CMD_BLE_CONF,
    WIFI_CMD_GETDID,
    WIFI_CMD_INVALID,
} wifi_command_e;

typedef enum
{
	WIFI_SHAKEHAND_NONE,
	WIFI_SHAKEHAND_ECHO_OFF,
	WIFI_SHAKEHAND_MODEL,
	WIFI_SHAKEHAND_MCU_VER,
	WIFI_SHAKEHAND_AUTOOTA,
#if HAVE_BLE
	WIFI_SHAKEHAND_BLE_CONF,
#endif
	WIFI_SHAKEHAND_COMPLETE,
	WIFI_SHAKEHAND_STEP_NUM = WIFI_SHAKEHAND_COMPLETE,
}wifi_shakehand_step;


typedef enum
{
	MCU_TO_WIFI_COMM_IDLE,
	MCU_TO_WIFI_COMM_PROP_CHANGED,
	MCU_TO_WIFI_COMM_EVENT_OCCURED,
	MCU_TO_WIFI_COMM_SEND_COMMAND,
}mcu_to_wifi_comm_status_e;

typedef void (*callbackCommandResult)(const char* pValue, char result);

typedef struct
{
	wifi_command_e command;
	callbackCommandResult commandCallback;
	bool is_execute;
}wifi_cmd_status_t;

extern wifi_cmd_status_t g_cmd_status;

int execute_wifi_cmd_async(wifi_command_e cmd, callbackCommandResult callback);

void Miio_Init(void);
void Miio_Count_1ms(void);
int miio_command_rx_tx(void);
int Miio_GetDeviceReportTimeMin(void);

int miio_strtok(const char * buf, char * delim);
int miio_strlen(const char *str);
int32_t miio_atoi(const char *str);
char *miio_get_result_buf(void);
int miio_encode_error_response(char *message, int errcode, char *presult);
void get_factory_data(char *result, uint16_t len);

#endif /* _MIIO_API_H_ */
