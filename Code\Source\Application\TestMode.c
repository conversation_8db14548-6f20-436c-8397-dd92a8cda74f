/*!
 * @file
 * @brief Manages all the state variables of the test mode.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "TestMode.h"
#include "Core_CallBackTimer.h"
#include "ResolverDevice.h"
#include "Driver_GradualLamp.h"
#include "IO_Device.h"
#include "Driver_DoubleDamper.h"
#include "Driver_CompFrequency.h"
#include "SystemManager.h"
#include "Driver_Fan.h"
#include "DisplayInterface.h"
#include "DisplayUsart.h"
#include "Driver_AdSample.h"
#include "LedController.h"
#include "miio_api.h"
#include "FridgeRunner.h"
#include "SpecialKeyManager.h"
#include "Drive_Valve.h"
#include "SystemTimerModule.h"



#define U16_TEST_MODE_CYCLE_SECOND (uint16_t)1

static st_CoreCallbackTimer st_TestModeTimer;
static uint16_t u16_TestControlCount;
static RoomTempRange_t test_room_range = 0;
static bool b_TestCtrlLoadOver;
//static uint8_t u8_TestMode;
static bool b_DamperInit;
static bool b_DefrostInit;
static uint8_t u8_TestModeSave;
static bool b_DefrostForce;


static void Stop_PollTimer(void)
{
    Core_CallbackTimer_TimerStop(&st_TestModeTimer);
}

static void TestMode_ControlLoad(void)
{
    #if(0)
    bool wifi_error = false;

    if(u16_TestControlCount < 1)
    {
        execute_wifi_cmd_async(WIFI_CMD_FACTORY, NULL);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, 0);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_Off);
        ForcedCtrl_DoubleDamper(false);
    }
    else if(u16_TestControlCount < 4)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_On);
    }
    else if(u16_TestControlCount < 7)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzDefHeater, DS_Off);
    }
    else if(u16_TestControlCount < 10)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_On);
    }
    else if(u16_TestControlCount < 13)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_VerticalBeamHeater, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 100);
    }
    else if(u16_TestControlCount < 16)
    {
        ;
    }
    else if(u16_TestControlCount < 19)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 0);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, RPM_100DUTY);
    }
    else if(u16_TestControlCount < 22)
    {
        ;
    }
    else if(u16_TestControlCount < 25)
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, RPM_0DUTY);
    }
    else if(u16_TestControlCount < 28)
    {
        ForcedCtrl_DoubleDamper(true);
    }
    else if(u16_TestControlCount < 31)
    {
        ForcedCtrl_DoubleDamper(false);
    }
    else if(u16_TestControlCount < 34)
    {
        ForcedCtrl_DoubleDamper(true);
    }
    else if(u16_TestControlCount < 37)
    {
        ForcedCtrl_DoubleDamper(false);
    }
    else
    {
        Reset_DoubleDamper();
        u16_TestControlCount = 0;
        b_TestCtrlLoadOver = true;
    }
    u16_TestControlCount++;
    #endif
}

static void TestMode_ControlCooling(void)
{
    if(false == b_DamperInit)
    {
        b_DamperInit = true;
        Reset_DoubleDamper();
        Drive_ValveReset();
        u16_TestControlCount = Get_MinuteCount();
    }

    if(b_DefrostInit == true)
    {
        Defrosting_Exit();
        Clear_DefrostMode();
        b_DefrostInit = false;
    }

    if(Get_MinuteElapsedTime(u16_TestControlCount) < 1)
    {
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_CoolFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefVarDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_AllOpen);
    }
    else if(Get_MinuteElapsedTime(u16_TestControlCount) < (72 * 60))
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, FREQ_147HZ);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 100);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 100);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLOPEN);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLOPEN);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 100);
    }
    else
    {
        TestMode_Exit();
        Exit_Tmode();
        FridgeState_Update(eFridge_Startup);
    }
}

static void TestMode_ControlFrzCooling(void)
{
    if(false == b_DamperInit)
    {
        b_DamperInit = true;
        Reset_DoubleDamper();
        Drive_ValveReset();
        u16_TestControlCount = Get_MinuteCount();
    }

    if(b_DefrostInit == true)
    {
        Defrosting_Exit();
        Clear_DefrostMode();
        b_DefrostInit = false;
    }

    if(Get_MinuteElapsedTime(u16_TestControlCount) < 1)
    {
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_CoolFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefVarDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_FrzON_RefOFF);
    }
    else if(Get_MinuteElapsedTime(u16_TestControlCount) < (72 * 60))
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, FREQ_147HZ);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 0);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 100);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLCLOSE);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLCLOSE);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 100);
    }
    else
    {
        TestMode_Exit();
        Exit_Tmode();
        FridgeState_Update(eFridge_Startup);
    }
}

static void TestMode_ControlRefCooling(void)
{
    if(false == b_DamperInit)
    {
        b_DamperInit = true;
        Reset_DoubleDamper();
        Drive_ValveReset();
        u16_TestControlCount = Get_MinuteCount();
    }

    if(b_DefrostInit == true)
    {
        Defrosting_Exit();
        Clear_DefrostMode();
        b_DefrostInit = false;
    }

    if(Get_MinuteElapsedTime(u16_TestControlCount) < 1)
    {
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_CoolFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefVarDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, Valve_FrzOFF_RefON);
    }
    else if(Get_MinuteElapsedTime(u16_TestControlCount) < (72 * 60))
    {
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, FREQ_147HZ);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, 100);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, 100);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, ACTIVE_DAMPER_STATE_ALLOPEN);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefVarDamper, SLAVE_DAMPER_STATE_ALLOPEN);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, 100);
    }
    else
    {
        TestMode_Exit();
        Exit_Tmode();
        FridgeState_Update(eFridge_Startup);
    }
}


static void TestMode_ControlDefrosting(void)
{
    DefrostMode_t defrost_mode = Get_DefrostMode();

    if(false == b_DamperInit)
    {
        b_DamperInit = true;
        Reset_DoubleDamper();
        Drive_ValveReset();
        u16_TestControlCount = Get_MinuteCount();
    }

    if(false == b_DefrostInit)
    {
        b_DefrostInit = true;
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_CoolFan, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefVarDamper, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_Off);
        Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDefHeater, DS_Off);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DS_DontCare);
        Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefVarDamper, DS_DontCare);
        Defrosting_Init(eEnterState_First);
        Set_DeforstExitTemp(U16_FORCE_DEFROST_EXIT_TEMPERATURE);
    }

    if((DefrostMode_t)eDefrostMode_AfterComp < defrost_mode)
    {
        Defrosting_Exit();
        Clear_DefrostMode();
        Exit_Tmode();
        FridgeState_Update(eFridge_Startup);
    }
}

static void TestMode_ControlRefDefrosting(void)
{
    
}

static void Process_TestMode(void)
{
    //uint8_t u8_TestMode = Get_TestMode();
    uint8_t u8_TestMode = Get_TestModeConfirm();

    if(u8_TestModeSave != u8_TestMode)
    {
        u8_TestModeSave = u8_TestMode;
        u16_TestControlCount = Get_MinuteCount();
        
    }

    switch(u8_TestMode)
    {
        case TMODE_FORCE_COOLING:
            TestMode_ControlCooling();
            break;
        case TMODE_FORCE_FRZCOOLING:
            TestMode_ControlFrzCooling();
            break;
        case TMODE_FORCE_REFCOOLING:
            TestMode_ControlRefCooling();
            break;
        case TMODE_FORCE_DEFROST:
            TestMode_ControlDefrosting();
            break;
        case TMODE_FORCE_REFDEFROST:
            //TestMode_ControlRefDefrosting();
            break;
        case TMODE_FORCE_ENERGY:
            //Force_EnterEnergyConsumptionModeState();
            //FridgeState_Update(eFridge_Startup);
            break;
        case TMODE_TT:
            //FridgeState_Update(eFridge_Startup);
            break;
        default:
            break;
    }
}

static void Start_PollTimer(uint16_t tickSec)
{
    Core_CallbackTimer_TimerStart(
        &st_TestModeTimer,
        Process_TestMode,
        tickSec,
        0,
        eCallbackTimer_Type_Periodic,
        eCallbackTimer_Priority_Normal);
}

void TestMode_Init(void)
{
    b_DamperInit = false;
    b_DefrostInit = false;
    test_room_range = Get_RoomTempRange();
    u8_TestModeSave = 0;
    Start_PollTimer(U16_TEST_MODE_CYCLE_SECOND);
}

void TestMode_Exit(void)
{
    if(b_DefrostInit == true)
    {
        Defrosting_Exit();
        Clear_DefrostMode();
        b_DefrostInit = false;
    }
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_Comp, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefFan, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzFan, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_CoolFan, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDamper, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefVarDamper, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_FrzDefHeater, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_IonGenerator, DS_Off);
    Vote_DeviceStatus(FSM_NormalControl, DEVICE_RefDefHeater, DS_Off);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Valve, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_Comp, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_FrzFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_CoolFan, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefDamper, DS_DontCare);
    Vote_DeviceStatus(FSM_SpecialModes, DEVICE_RefVarDamper, DS_DontCare);
    Stop_PollTimer();
    Set_CoolingEntryMode(eMode_FridgePowerOn);
}
