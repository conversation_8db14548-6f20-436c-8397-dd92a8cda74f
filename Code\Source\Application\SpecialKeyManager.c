/*!
 * @file
 * @brief Manages all the state variables of the special key.
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "SpecialKeyManager.h"
#include "KeyManager.h"
#include "SystemTimerModule.h"
#include "DisplayInterface.h"
#include "Parameter_TemperatureZone.h"
#include "DisplayUsart.h"
#include "VerticalBeamHeater.h"
#include "FridgeRunner.h"
#include "SystemManager.h"
#include "Driver_Flash.h"
#include "Driver_Fan.h"
#include "TestMode.h"


static void Deal_CLearEEKey(void);
static void Deal_ShowroomKey(void);
static void Deal_QueryDataKey(void);
static void Deal_TmodeKey(void);

Query_em em_QueryMode;
uint8_t u8_QueryData;
bool f_QueryError;

Tmode_em u8_Tmode;
Tmode_em u8_TmodeX;

static bool b_ShowroomMode = false;
static bool b_SpecialMode = false;
static bool b_CompAdjust;
static uint16_t u16_CompAdjustFreq;
static bool b_RefFanAdjust;
static uint8_t u8_RefFanAdjustDuty;
static bool b_FrzFanAdjust;
static uint8_t u8_FrzFanAdjustDuty;
static bool b_CoolFanAdjust;
static uint8_t u8_CoolFanAdjustPara;
static bool b_RefOnAdjust;
static int8_t i8_RefOnAdjustValue;
static bool b_RefOffAdjust;
static int8_t i8_RefOffAdjustValue;
static bool b_RefVarOnAdjust;
static int8_t i8_RefVarOnAdjustValue;
static bool b_RefVarOffAdjust;
static int8_t i8_RefVarOffAdjustValue;
static bool b_FrzOnAdjust;
static int8_t i8_FrzOnAdjustValue;
static bool b_FrzOffAdjust;
static int8_t i8_FrzOffAdjustValue;

/***************************Unite Key Begin**************************************/
#define SIZE_UNITEKEY_ARY (sizeof(ary_UniteKey) / sizeof(UniteKey_st))
#define CON_UNITE_KEY_CANCEL_TIME 8 // s
// Long press frz,click mode 5 times
#define MULTI_KEY_TESTMODE (KEY_VALUE_FRZ + KEY_VALUE_MODE)
// Long press frz,click mode 5 times
#define MULTI_KEY_CLEAR_EEPROM (KEY_VALUE_REF + KEY_VALUE_MODE)
// Long press ref,click frz 5 times
#define MULTI_KEY_QUERY_DATA (KEY_VALUE_REF + KEY_VALUE_FRZ)

// clang-format off
const UniteKey_st ary_UniteKey[] = {
// p_Deal_KeyActionCompleted,    u16_UniteKeyValue,       u16_SingleKeyValue, u8_NeedCounter,b_IsValid
    { Deal_TmodeKey,             MULTI_KEY_TESTMODE,        KEY_VALUE_FRZ,           5,       true },
    { Deal_CLearEEKey,           MULTI_KEY_CLEAR_EEPROM,    KEY_VALUE_REF,           5,       true },
    { Deal_QueryDataKey,         MULTI_KEY_QUERY_DATA,      KEY_VALUE_REF,           5,       true }    
};
// clang-format on

// 查询组合按键数组，返回是否查询到按键值 true 查询到
bool Inquire_UniteKeyAry(unsigned int u16_currentKey, unsigned int u16_preKey, unsigned char *u8_activeI)
{
    bool b_result = false;
    uint8_t u8_index = 0;

    for(u8_index = 0; u8_index < SIZE_UNITEKEY_ARY; u8_index++)
    {
        if(false == ary_UniteKey[u8_index].b_IsValid)
        {
            continue;
        }

        if((ary_UniteKey[u8_index].u16_UniteKeyValue == u16_currentKey) &&
            (ary_UniteKey[u8_index].u16_SingleKeyValue == u16_preKey))
        {
            b_result = true;
            *u8_activeI = u8_index;
            break;
        }
    }

    return b_result;
}

static void Deal_UniteKeyProcess(uint16_t u16_keyValue)
{
    static uint16_t u16_timeSec;
    static uint8_t u8_keyCount;
    static uint8_t u8_activateI;
    static uint16_t u16_preKeyValue; // 前一次按键值
    static uint8_t u8_keyCancelDelay; // 按键取消倒计时
    uint8_t u8_index = 0;
    uint16_t u16_nowSec = Get_SecondCount();

    if(u8_keyCancelDelay)
    {
        if((uint16_t)(u16_nowSec - u16_timeSec) > 0)
        {
            u16_timeSec = u16_nowSec;
            u8_keyCancelDelay--;
        }
    }
    else
    {
        u8_keyCount = 0;
    }

    if(u16_preKeyValue == u16_keyValue)
    {
        return;
    }

    if(0 == u8_keyCount)
    {
        for(u8_index = 0; u8_index < SIZE_UNITEKEY_ARY; u8_index++)
        {
            if(false == ary_UniteKey[u8_index].b_IsValid)
            {
                continue;
            }

            if(ary_UniteKey[u8_index].u16_UniteKeyValue == u16_keyValue)
            {
                u8_keyCount = 1;
                u8_activateI = u8_index;
                u8_keyCancelDelay = CON_UNITE_KEY_CANCEL_TIME;
                break;
            }
        }
    }
    else if(ary_UniteKey[u8_activateI].u16_UniteKeyValue == u16_keyValue)
    {
        u8_keyCount++;

        if(u8_keyCount >= ary_UniteKey[u8_activateI].u8_NeedCounter)
        {
            u8_keyCount = 0;
            u8_keyCancelDelay = 0;
            ary_UniteKey[u8_activateI].p_Deal_KeyActionCompleted();
        }
    }
    else if(ary_UniteKey[u8_activateI].u16_SingleKeyValue != u16_keyValue)
    {
        u8_keyCount = 0;

        if(false == Inquire_UniteKeyAry(u16_preKeyValue, u16_keyValue, &u8_activateI))
        {
            if(true == Inquire_UniteKeyAry(u16_keyValue, u16_preKeyValue, &u8_activateI))
            {
                u8_keyCount = 1;
                u8_keyCancelDelay = CON_UNITE_KEY_CANCEL_TIME;
            }
        }
        else
        {
            u8_keyCount = 1;
            u8_keyCancelDelay = CON_UNITE_KEY_CANCEL_TIME;
        }
    }

    u16_preKeyValue = u16_keyValue;
}

/***************************Unite Key End**************************************/
void Deal_ShowroomKey(void)
{
    if(true == Get_LockStatus())
    {
        return;
    }

    if(b_ShowroomMode)
    {
        b_ShowroomMode = false;
        // Exit_ShowroomDisp();
        Set_MusicType((MusicType_t)eLong_Tone);
        SetOver();
    }
    else if(false == b_SpecialMode)
    {
        b_ShowroomMode = true;
        // Enter_ShowroomDisp();
        Set_MusicType((MusicType_t)eLong_Tone);
        SetOver();
    }
}

static void Deal_CLearEEKey(void)
{
    if(true == Get_LockStatus())
    {
        return;
    }

    if(true == b_SpecialMode)
    {
        return;
    }

    SetOver();
}

static void Ctrl_QueryDataMode(void)
{
    static uint16_t u16_timeSec;
    static uint8_t u8_preRTNum;
    static uint8_t u8_exitRealTimeDateDelay;
    uint16_t u16_nowSec = Get_SecondCount();

    if(0 == em_QueryMode)
    {
        u8_exitRealTimeDateDelay = TIME_QUERYDATEEXIT;
        u8_preRTNum = 0;
        u16_timeSec = u16_nowSec;
    }
    else
    {
        if(u8_preRTNum != em_QueryMode)
        {
            u8_preRTNum = em_QueryMode;
            u8_exitRealTimeDateDelay = TIME_QUERYDATEEXIT;
            u16_timeSec = u16_nowSec;
        }

        if((uint16_t)(u16_nowSec - u16_timeSec) > 0)
        {
            u16_timeSec = u16_nowSec;

            if(u8_exitRealTimeDateDelay)
            {
                u8_exitRealTimeDateDelay--;
                Wakeup_UserInterface();
            }
            else
            {
                em_QueryMode = 0;
            }
        }
        Set_QueryModeDisplay();
    }
}

static void Adjust_QueryDataMode(void)
{
    if(em_QueryMode)
    {
        if(em_QueryMode >= CHECK_MAX - 1)
        {
            em_QueryMode = 1;
        }
        else
        {
            em_QueryMode++;
        }
	}
}

void Inc_AdjustPara(void)
{
    switch(em_QueryMode)
    {
        case CHECK_COMP:
            if(false == b_CompAdjust)
            {
                b_CompAdjust = true;
                u16_CompAdjustFreq = Get_CompFreqMicroAdjustParm();
                if(0 == u16_CompAdjustFreq)
                {
                    u16_CompAdjustFreq = 70;
                }
            }
            else
            {
                if(U16_COMP_FREQINDEX_MAX > u16_CompAdjustFreq)
                {
                    u16_CompAdjustFreq++;
                }

                if((u16_CompAdjustFreq > 0) && (u16_CompAdjustFreq < 40))
                {
                    u16_CompAdjustFreq = 40;
                }
            }
            Set_AdjustParaDisplay((uint8_t)u16_CompAdjustFreq, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;

        case CHECK_REF_FAN_DUTY:
            if(false == b_RefFanAdjust)
            {
                b_RefFanAdjust = true;
                u8_RefFanAdjustDuty = Get_RefFanAdjustParm();
                if(0 == u8_RefFanAdjustDuty)
                {
                    u8_RefFanAdjustDuty = 69;
                }
            }
            else
            {
                if(100 > u8_RefFanAdjustDuty)
                {
                    u8_RefFanAdjustDuty++;
                }
            }
            Set_AdjustParaDisplay((uint8_t)u8_RefFanAdjustDuty, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
			
        case CHECK_FRZ_FAN_DUTY:
            if(false == b_FrzFanAdjust)
            {
                b_FrzFanAdjust = true;
                u8_FrzFanAdjustDuty = Get_FrzFanAdjustParm();
                if(0 == u8_FrzFanAdjustDuty)
                {
                    u8_FrzFanAdjustDuty = 69;
                }
            }
            else
            {
                if(100 > u8_FrzFanAdjustDuty)
                {
                    u8_FrzFanAdjustDuty++;
                }
            }
            Set_AdjustParaDisplay((uint8_t)u8_FrzFanAdjustDuty, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        case CHECK_COND_FAN_DUTY:
            if(false == b_CoolFanAdjust)
            {
                b_CoolFanAdjust = true;
                u8_CoolFanAdjustPara = Get_CoolFanAdjustParm();
                if(0 == u8_CoolFanAdjustPara)
                {
                    u8_CoolFanAdjustPara = 40;	//RPM_40DUTY
                }
            }
            else
            {
                if(100 > u8_CoolFanAdjustPara)//RPM_100DUTY
                {
                    u8_CoolFanAdjustPara++;
                }
            }
            Set_AdjustParaDisplay((uint8_t)u8_CoolFanAdjustPara, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        case CHECK_REF_ON:
            if(false == b_RefOnAdjust)
            {
                b_RefOnAdjust = true;
                i8_RefOnAdjustValue = (int8_t)Get_RefOnTempMicroAdjustParm();
            }
            else
            {
                if(i8_RefOnAdjustValue < 120)
                {
                    i8_RefOnAdjustValue++;
                }
            }
            Set_AdjustParaDisplay((uint8_t)i8_RefOnAdjustValue, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        case CHECK_REF_OFF:
            if(false == b_RefOffAdjust)
            {
                b_RefOffAdjust = true;
                i8_RefOffAdjustValue = (int8_t)Get_RefOffTempMicroAdjustParm();
            }
            else
            {
                if(i8_RefOffAdjustValue < 120)
                {
                    i8_RefOffAdjustValue++;
                }
            }
            Set_AdjustParaDisplay((uint8_t)i8_RefOffAdjustValue, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        case CHECK_FRZ_ON:
            if(false == b_FrzOnAdjust)
            {
                b_FrzOnAdjust = true;
                i8_FrzOnAdjustValue = (int8_t)Get_FrzOnTempMicroAdjustParm();
            }
            else
            {
                if(i8_FrzOnAdjustValue < 120)
                {
                    i8_FrzOnAdjustValue++;
                }
            }
            Set_AdjustParaDisplay((uint8_t)i8_FrzOnAdjustValue, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        case CHECK_FRZ_OFF:
            if(false == b_FrzOffAdjust)
            {
                b_FrzOffAdjust = true;
                i8_FrzOffAdjustValue = (int8_t)Get_FrzOffTempMicroAdjustParm();
            }
            else
            {
                if(i8_FrzOffAdjustValue < 120)
                {
                    i8_FrzOffAdjustValue++;
                }
            }
            Set_AdjustParaDisplay((uint8_t)i8_FrzOffAdjustValue, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        case CHECK_VAR_ON:
            if(false == b_RefVarOnAdjust)
            {
                b_RefVarOnAdjust = true;
                i8_RefVarOnAdjustValue = (int8_t)Get_RefVarOnTempMicroAdjustParm();
            }
            else
            {
                if(i8_RefVarOnAdjustValue < 120)
                {
                    i8_RefVarOnAdjustValue++;
                }
            }
            Set_AdjustParaDisplay((uint8_t)i8_RefVarOnAdjustValue, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        case CHECK_VAR_OFF:
            if(false == b_RefVarOffAdjust)
            {
                b_RefVarOffAdjust = true;
                i8_RefVarOffAdjustValue = (int8_t)Get_RefVarOffTempMicroAdjustParm();
            }
            else
            {
                if(i8_RefVarOffAdjustValue < 120)
                {
                    i8_RefVarOffAdjustValue++;
                }
            }
            Set_AdjustParaDisplay((uint8_t)i8_RefVarOffAdjustValue, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        default:
            break;
    }
}

void Dec_AdjustPara(void)
{
    switch(em_QueryMode)
    {
        case CHECK_COMP:
            if(false == b_CompAdjust)
            {
                b_CompAdjust = true;
                u16_CompAdjustFreq = Get_CompFreqMicroAdjustParm();
                if(0 == u16_CompAdjustFreq)
                {
                    u16_CompAdjustFreq = 70;
                }
            }
            else
            {
                if(U16_COMP_FREQINDEX_MIN > u16_CompAdjustFreq)
                {
                    u16_CompAdjustFreq = 0;
                }
                else
                {
                    u16_CompAdjustFreq--;
                }
            }
            Set_AdjustParaDisplay((uint8_t)u16_CompAdjustFreq, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;

        case CHECK_REF_FAN_DUTY:
            if(false == b_RefFanAdjust)
            {
                b_RefFanAdjust = true;
                u8_RefFanAdjustDuty = Get_RefFanAdjustParm();
                if(0 == u8_RefFanAdjustDuty)
                {
                    u8_RefFanAdjustDuty = 69;
                }
            }
            else
            {
                if(50 > u8_RefFanAdjustDuty)
                {
                    u8_RefFanAdjustDuty = 0;
                }
                else
                {
                    u8_RefFanAdjustDuty--;
                }
            }
            Set_AdjustParaDisplay((uint8_t)u8_RefFanAdjustDuty, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
            
        case CHECK_FRZ_FAN_DUTY:
            if(false == b_FrzFanAdjust)
            {
                b_FrzFanAdjust = true;
                u8_FrzFanAdjustDuty = Get_FrzFanAdjustParm();
                if(0 == u8_FrzFanAdjustDuty)
                {
                    u8_FrzFanAdjustDuty = 69;
                }
            }
            else
            {
                if(50 > u8_FrzFanAdjustDuty)
                {
                    u8_FrzFanAdjustDuty = 0;
                }
                else
                {
                    u8_FrzFanAdjustDuty--;
                }
            }
            Set_AdjustParaDisplay((uint8_t)u8_FrzFanAdjustDuty, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        case CHECK_COND_FAN_DUTY:
            if(false == b_CoolFanAdjust)
            {
                b_CoolFanAdjust = true;
                u8_CoolFanAdjustPara = Get_CoolFanAdjustParm();
                if(0 == u8_CoolFanAdjustPara)
                {
                    u8_CoolFanAdjustPara = 40;	//RPM_40DUTY
                }
            }
            else
            {
                if(0 < u8_CoolFanAdjustPara)
                {
                    u8_CoolFanAdjustPara--;
                }
            }
            Set_AdjustParaDisplay((uint8_t)u8_CoolFanAdjustPara, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        case CHECK_REF_ON:
            if(false == b_RefOnAdjust)
            {
                b_RefOnAdjust = true;
                i8_RefOnAdjustValue = (int8_t)Get_RefOnTempMicroAdjustParm();
            }
            else
            {
                if(i8_RefOnAdjustValue > -120)
                {
                    i8_RefOnAdjustValue--;
                }
            }
            Set_AdjustParaDisplay((uint8_t)i8_RefOnAdjustValue, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        case CHECK_REF_OFF:
            if(false == b_RefOffAdjust)
            {
                b_RefOffAdjust = true;
                i8_RefOffAdjustValue = (int8_t)Get_RefOffTempMicroAdjustParm();
            }
            else
            {
                if(i8_RefOffAdjustValue > -120)
                {
                    i8_RefOffAdjustValue--;
                }
            }
            Set_AdjustParaDisplay((uint8_t)i8_RefOffAdjustValue, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        case CHECK_FRZ_ON:
            if(false == b_FrzOnAdjust)
            {
                b_FrzOnAdjust = true;
                i8_FrzOnAdjustValue = (int8_t)Get_FrzOnTempMicroAdjustParm();
            }
            else
            {
                if(i8_FrzOnAdjustValue > -120)
                {
                    i8_FrzOnAdjustValue--;
                }
            }
            Set_AdjustParaDisplay((uint8_t)i8_FrzOnAdjustValue, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        case CHECK_FRZ_OFF:
            if(false == b_FrzOffAdjust)
            {
                b_FrzOffAdjust = true;
                i8_FrzOffAdjustValue = (int8_t)Get_FrzOffTempMicroAdjustParm();
            }
            else
            {
                if(i8_FrzOffAdjustValue > -120)
                {
                    i8_FrzOffAdjustValue--;
                }
            }
            Set_AdjustParaDisplay((uint8_t)i8_FrzOffAdjustValue, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        case CHECK_VAR_ON:
            if(false == b_RefVarOnAdjust)
            {
                b_RefVarOnAdjust = true;
                i8_RefVarOnAdjustValue = (int8_t)Get_RefVarOnTempMicroAdjustParm();
            }
            else
            {
                if(i8_RefVarOnAdjustValue > -120)
                {
                    i8_RefVarOnAdjustValue--;
                }
            }
            Set_AdjustParaDisplay((uint8_t)i8_RefVarOnAdjustValue, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        case CHECK_VAR_OFF:
            if(false == b_RefVarOffAdjust)
            {
                b_RefVarOffAdjust = true;
                i8_RefVarOffAdjustValue = (int8_t)Get_RefVarOffTempMicroAdjustParm();
            }
            else
            {
                if(i8_RefVarOffAdjustValue > -120)
                {
                    i8_RefVarOffAdjustValue--;
                }
            }
            Set_AdjustParaDisplay((uint8_t)i8_RefVarOffAdjustValue, true);
            Set_MusicType((MusicType_t)eShort_Tone);
            SetOver();
            break;
        default:
            break;
    }
}

void Confirm_AdjustPara(void)
{
    switch(em_QueryMode)
    {
        case CHECK_COMP:
            Set_CompFreqMicroAdjustParm(u16_CompAdjustFreq);
            Set_AdjustParaDisplay((uint8_t)u16_CompAdjustFreq, false);
            Set_MusicType((MusicType_t)eLong_Tone);
            SetOver();
            break;
        case CHECK_REF_FAN_DUTY:
            Set_FrzFanAdjustParm(u8_RefFanAdjustDuty);
            Set_AdjustParaDisplay(u8_RefFanAdjustDuty, false);
            Set_MusicType((MusicType_t)eLong_Tone);
            break;
        case CHECK_FRZ_FAN_DUTY:
            Set_FrzFanAdjustParm(u8_FrzFanAdjustDuty);
            Set_AdjustParaDisplay(u8_FrzFanAdjustDuty, false);
            Set_MusicType((MusicType_t)eLong_Tone);
            break;
        case CHECK_COND_FAN_DUTY:
            Set_CoolFanAdjustParm(u8_CoolFanAdjustPara);
            Set_AdjustParaDisplay(u8_CoolFanAdjustPara, false);
            Set_MusicType((MusicType_t)eLong_Tone);
            break;
        case CHECK_REF_ON:
            Set_RefOnTempMicroAdjustParm(i8_RefOnAdjustValue);
            Set_AdjustParaDisplay(i8_RefOnAdjustValue, false);
            Set_MusicType((MusicType_t)eLong_Tone);
            break;
        case CHECK_REF_OFF:
            Set_RefOffTempMicroAdjustParm(i8_RefOffAdjustValue);
            Set_AdjustParaDisplay(i8_RefOffAdjustValue, false);
            Set_MusicType((MusicType_t)eLong_Tone);
            break;
        case CHECK_FRZ_ON:
            Set_FrzOnTempMicroAdjustParm(i8_FrzOnAdjustValue);
            Set_AdjustParaDisplay(i8_FrzOnAdjustValue, false);
            Set_MusicType((MusicType_t)eLong_Tone);
            break;
        case CHECK_FRZ_OFF:
            Set_FrzOffTempMicroAdjustParm(i8_FrzOffAdjustValue);
            Set_AdjustParaDisplay(i8_FrzOffAdjustValue, false);
            Set_MusicType((MusicType_t)eLong_Tone);
            break;
        case CHECK_VAR_ON:
            Set_RefVarOnTempMicroAdjustParm(i8_RefVarOnAdjustValue);
            Set_AdjustParaDisplay(i8_RefVarOnAdjustValue, false);
            Set_MusicType((MusicType_t)eLong_Tone);
            break;
        case CHECK_VAR_OFF:
            Set_RefVarOffTempMicroAdjustParm(i8_RefVarOffAdjustValue);
            Set_AdjustParaDisplay(i8_RefVarOffAdjustValue, false);
            Set_MusicType((MusicType_t)eLong_Tone);
            break;
        default:
            break;
    }
}

void Deal_QueryDataKey(void)
{
    if(true == Get_LockStatus())
    {
        return;
    }

    if(em_QueryMode)
    {
        em_QueryMode = 0;
        Set_MusicType((MusicType_t)eLong_Tone);
        SetOver();
    }
    else if(false == b_SpecialMode)
    {
        em_QueryMode = 1;
        Set_MusicType((MusicType_t)eLong_Tone);
        SetOver();
    }
}

static void Deal_TmodeKey(void)
{
    if(true == Get_LockStatus())
    {
        return;
    }

    if(u8_TmodeX)
    {
        u8_TmodeX = 0;
        Set_MusicType((MusicType_t)eLong_Tone);
        SetOver();
    }
    else if(false == b_SpecialMode)
    {
        u8_TmodeX = TMODE_TT;
        Set_TestModeDisplay(true);
        Set_MusicType((MusicType_t)eLong_Tone);
        SetOver();
    }
}

void TMode_Display(ZoneDisp_st *pst_disp)
{
    pst_disp->b_C = false;
    pst_disp->b_Minus = false;

    /*
    switch(u8_TmodeX)
    {
        case TMODE_FORCE_COOLING:	//T1
            pst_disp->u8_High = CON_DispT;
            pst_disp->u8_Low = CON_Disp1;
            break;
        case TMODE_FORCE_FRZCOOLING:	//TF
            pst_disp->u8_High = CON_DispT;
            pst_disp->u8_Low = CON_DispF;
            break;
        case TMODE_FORCE_REFCOOLING:	//Tr
            pst_disp->u8_High = CON_DispT;
            pst_disp->u8_Low = CON_DispR;
            break;
        case TMODE_FORCE_DEFROST:
            pst_disp->u8_High = CON_DispH;
            pst_disp->u8_Low = CON_DispH;
            break;
        case TMODE_FORCE_ENERGY:
            pst_disp->u8_High = CON_DispP;
            pst_disp->u8_Low = CON_Disp0;
            break;
        case TMODE_TT:
            pst_disp->u8_High = CON_DispT;
            pst_disp->u8_Low = CON_DispT;
            break;
        default:
            break;
    }
    */
}

static void Adjust_TMode(void)
{
    if(u8_TmodeX >= TMODE_TT)
    {
        //u8_TmodeX = TMODE_T1;
        u8_TmodeX = TMODE_FORCE_COOLING;
    }
    else
    {
        u8_TmodeX++;
    }
    Set_TestModeDisplay(true);
}

void Exit_Tmode(void)
{
    u8_Tmode = TMODE_NONE;
    u8_TmodeX = TMODE_NONE;
}

static void Ctrl_Tmode(void)
{
    static uint16_t u16_timeSec;
    static uint8_t u8_preTmodeX;
    static uint8_t u8_TmodeTimerSec;
    uint16_t u16_nowSec = Get_SecondCount();
    DefrostMode_t defrost_mode = Get_DefrostMode();

    if(0 == u8_TmodeX)
    {
        u8_Tmode = 0;
        u8_preTmodeX = 0;
        return;
    }
    Wakeup_UserInterface();

    if(u8_preTmodeX != u8_TmodeX)
    {
        u8_preTmodeX = u8_TmodeX;

        if(TMODE_TT == u8_TmodeX)
        {
            u8_TmodeTimerSec = 10;
        }
        else
        {
            u8_TmodeTimerSec = 5;
        }
    }

    /*T模式确认与自动退出判断*/
    if((uint16_t)(u16_nowSec - u16_timeSec) > 0)
    {
        u16_timeSec = u16_nowSec;

        if(u8_TmodeTimerSec)
        {
            u8_TmodeTimerSec--;
        }
        else
        {
            if(TMODE_TT == u8_TmodeX)
            {
            	if(u8_Tmode != u8_TmodeX)
                {
                    switch(u8_Tmode)
                    {
                        case TMODE_FORCE_COOLING:
                        case TMODE_FORCE_FRZCOOLING:
                        case TMODE_FORCE_REFCOOLING:
                            TestMode_Exit();
                            FridgeState_Update(eFridge_Startup);
                            break;
                            
                        case TMODE_FORCE_DEFROST:
                            //if((DefrostMode_t)eDefrostMode_AfterComp < defrost_mode)
                            {
                                Defrosting_Exit();
                                Clear_DefrostMode();
                                FridgeState_Update(eFridge_Startup);
                            }
                            break;
                            
                        case TMODE_FORCE_REFDEFROST:
                        case TMODE_FORCE_ENERGY:
                        case TMODE_TT:
                        default:
                            break;
                    }
                }
                Exit_Tmode();
            }
            else
            {
                //u8_Tmode = u8_TmodeX;

                if(u8_Tmode != u8_TmodeX)
                {
                    u8_Tmode = u8_TmodeX;
                    switch(u8_TmodeX)
                    {
                        case TMODE_FORCE_COOLING:
                        case TMODE_FORCE_FRZCOOLING:
                        case TMODE_FORCE_REFCOOLING:
                        case TMODE_FORCE_DEFROST:
                            FridgeState_Update(eFridge_Test);
                            break;
                        case TMODE_FORCE_REFDEFROST:
                            Force_Set_RefFrostReduceMode();
                            Exit_Tmode();
                            break;
                        case TMODE_FORCE_ENERGY:
                            Force_EnterEnergyConsumptionModeState();
                            Exit_Tmode();
                            break;
                        case TMODE_TT:
                            //FridgeState_Update(eFridge_Startup);
                            //Exit_Tmode();
                            break;
                        default:
                            break;
                    }
                }
            }

            Set_TestModeDisplay(false);
        }
    }
}

void Adjust_SpecialMode(void)
{
    if(u8_TmodeX)
    {
        Adjust_TMode();
        Set_MusicType((MusicType_t)eLong_Tone);
        SetOver();
    }
    else if(em_QueryMode)
    {
        Adjust_QueryDataMode();
        Set_MusicType((MusicType_t)eLong_Tone);
        SetOver();
    }
}

void Ctrl_SpecialKey(uint16_t u16_keyValue)
{
    Ctrl_Tmode();
    Ctrl_QueryDataMode();
    Deal_UniteKeyProcess(u16_keyValue);
    b_SpecialMode = u8_TmodeX || em_QueryMode;
}

uint8_t Get_TestMode(void)
{
    return (u8_TmodeX);
}

uint8_t Get_TestModeConfirm(void)
{
    return (u8_Tmode);
}

uint8_t Get_QueryMode(void)
{
    return ((uint8_t)em_QueryMode);
}

bool Get_SpecialMode(void)
{
    return (b_SpecialMode);
}
