/*!
 * @file
 * @brief This file defines public constants, types and functions for the system manager.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef DISPLAY_INTERFACE_H
#define DISPLAY_INTERFACE_H

#include <stdint.h>
#include <stdbool.h>
#include "SimpleFsm.h"

enum
{
    eUiStartup_State,
    eUiActive_State,
    eUiSleep_State
};
typedef uint8_t UiState_t;

enum
{
    eManual_Mode,
    eFuzzy_Mode,
    eTurboCool_Mode,
    eTurboFreeze_Mode,
    eMax_Mode
};
typedef uint8_t UserMode_t;

void UserInterface_Init(void);
void Wakeup_UserInterface(void);
void Restart_UserInterface(void);
void Process_UserMode(void);
UiState_t Get_UiState(void);
void Set_UserMode(UserMode_t mode);
void Update_RefSetTempBak(uint8_t u8_SetTemp);
void Update_FrzSetTempBak(uint8_t u8_SetTemp);
uint8_t Get_RefSetTempBak(void);
uint8_t Get_FrzSetTempBak(void);
UserMode_t Get_UserMode(void);
uint16_t Get_TurboCoolTimeMinute(void);
uint16_t Get_TurboFreezeTimeMinute(void);

#endif
