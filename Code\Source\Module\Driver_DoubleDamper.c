/*!
 * @file
 * @brief the single damper driver.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stddef.h>
#include "Driver_DoubleDamper.h"
#include "Adpt_GPIO.h"
#include "stdio.h"
#include "FridgeRunner.h"
#include "SystemManager.h"


static DoubleDamperDriver_st st_DoubleDamperDriver;

static const DoubleDamperState_em ary_DoubleDamperSate
    [(uint8_t)SLAVE_DAMPER_STATE_MAX][(uint8_t)ACTIVE_DAMPER_STATE_MAX] = {
        // SLAVE_DAMPER_STATE_ALLCLOSE
        {
            DOUBLE_DAMPER_STATE_ALLCLOSE, // ACTIVE_DAMPER_STATE_ALLCLOSE
            DOUBLE_DAMPER_STATE_ACTIVEOPEN_SLAVECLOSE // ACTIVE_DAMPER_STATE_ALLOPEN
        },

        // SLAVE_DAMPER_STATE_ALLOPEN
        {
            DOUBLE_DAMPER_STATE_ACTIVECLOSE_SLAVEOPEN, // ACTIVE_DAMPER_STATE_ALLCLOSE
            DOUBLE_DAMPER_STATE_ALLOPEN // ACTIVE_DAMPER_STATE_ALLOPEN
        }
    };

static const uint16_t ary_DoubleDamperRunData
    [(uint8_t)DOUBLE_DAMPER_STATE_MAX][(uint8_t)DOUBLE_DAMPER_STATE_MAX] = {
        // DOUBLE_DAMPER_STATE_ALLCLOSE_LIMIT
        {
            0, // DOUBLE_DAMPER_STATE_ALLCLOSE_LIMIT
            2110, // DOUBLE_DAMPER_STATE_ALLCLOSE
            4220, // DOUBLE_DAMPER_STATE_ACTIVEOPEN_SLAVECLOSE
            6330, // DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT
            0, // DOUBLE_DAMPER_STATE_ALLOPEN
            0 // DOUBLE_DAMPER_STATE_ACTIVECLOSE_SLAVEOPEN
        },

        // DOUBLE_DAMPER_STATE_ALLCLOSE
        {
            0, // DOUBLE_DAMPER_STATE_ALLCLOSE_LIMIT
            0, // DOUBLE_DAMPER_STATE_ALLCLOSE
            2110, // DOUBLE_DAMPER_STATE_ACTIVEOPEN_SLAVECLOSE
            4220, // DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT
            0, // DOUBLE_DAMPER_STATE_ALLOPEN
            0 // DOUBLE_DAMPER_STATE_ACTIVECLOSE_SLAVEOPEN
        },

        // DOUBLE_DAMPER_STATE_ACTIVEOPEN_SLAVECLOSE
        {
            0, // DOUBLE_DAMPER_STATE_ALLCLOSE_LIMIT
            0, // DOUBLE_DAMPER_STATE_ALLCLOSE
            0, // DOUBLE_DAMPER_STATE_ACTIVEOPEN_SLAVECLOSE
            2110, // DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT
            0, // DOUBLE_DAMPER_STATE_ALLOPEN
            0 // DOUBLE_DAMPER_STATE_ACTIVECLOSE_SLAVEOPEN
        },

        // DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT
        {
            6330, // DOUBLE_DAMPER_STATE_ALLCLOSE_LIMIT
            0, // DOUBLE_DAMPER_STATE_ALLCLOSE
            0, // DOUBLE_DAMPER_STATE_ACTIVEOPEN_SLAVECLOSE
            0, // DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT
            2110, // DOUBLE_DAMPER_STATE_ALLOPEN
            4220 // DOUBLE_DAMPER_STATE_ACTIVECLOSE_SLAVEOPEN
        },

        // DOUBLE_DAMPER_STATE_ALLOPEN
        {
            4220, // DOUBLE_DAMPER_STATE_ALLCLOSE_LIMIT
            0, // DOUBLE_DAMPER_STATE_ALLCLOSE
            0, // DOUBLE_DAMPER_STATE_ACTIVEOPEN_SLAVECLOSE
            0, // DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT
            0, // DOUBLE_DAMPER_STATE_ALLOPEN
            2110 // DOUBLE_DAMPER_STATE_ACTIVECLOSE_SLAVEOPEN
        },

        // DOUBLE_DAMPER_STATE_ACTIVECLOSE_SLAVEOPEN
        {
            2110, // DOUBLE_DAMPER_STATE_ALLCLOSE_LIMIT
            0, // DOUBLE_DAMPER_STATE_ALLCLOSE
            0, // DOUBLE_DAMPER_STATE_ACTIVEOPEN_SLAVECLOSE
            0, // DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT
            0, // DOUBLE_DAMPER_STATE_ALLOPEN
            0 // DOUBLE_DAMPER_STATE_ACTIVECLOSE_SLAVEOPEN
        }
    };

static void Set_DoubleDamperStepState(DoubleDamperDriver_st *p_damper);
static void Set_DoubleDamperIOState(DoubleDamperDriver_st *p_damper);
static void Config_DoubleDamperIO(bool b_IO_En, bool b_IO_InA, bool b_IO_InB);
static void Get_DoubleDamperRunPara(void);

void Init_DoubleDamper(void)
{
    DoubleDamperDriver_st *p_damper = &st_DoubleDamperDriver;

    p_damper->u16_ActiveDamperNoActionSecond = 0;
    p_damper->u16_SlaveDamperNoActionSecond = 0;

    p_damper->u16_ActiveDamperZoneSnrValue = 0;
    p_damper->u16_SlaveDamperZoneSnrValue = 0;
    p_damper->u16_DoubleDamperRunSteps = 0;

    p_damper->em_DoubleDamperNowState = DOUBLE_DAMPER_STATE_ALLCLOSE;
    p_damper->em_DoubleDamperMidState = DOUBLE_DAMPER_STATE_ALLCLOSE;
    p_damper->em_DoubleDamperNewState = DOUBLE_DAMPER_STATE_ALLCLOSE;

    p_damper->em_ActiveDamperNowState = ACTIVE_DAMPER_STATE_ALLCLOSE;
    p_damper->em_ActiveDamperNewState = ACTIVE_DAMPER_STATE_ALLCLOSE;

    p_damper->em_SlaveDamperNowState = SLAVE_DAMPER_STATE_ALLCLOSE;
    p_damper->em_SlaveDamperNewState = SLAVE_DAMPER_STATE_ALLCLOSE;

    p_damper->em_DoubleDamperStepState = DOUBLE_DAMPER_STEP_STATE_A;

    p_damper->u8_DoubleDamperPPSTimer = U8_DOUBLE_DAMPER_RUN_PPS_TIME_HALF_MSEL;
    p_damper->u8_DoubleDamperIdleTimer = U8_DOUBLE_DAMPER_START_DELAY_TIME_SECOND;

    p_damper->f_DoubleDamperInitialized = true;
    p_damper->f_DoubleDamperRunning = false;
    p_damper->f_DoubleDamperNeedToReset = false;
    p_damper->f_DoubleDamperRunDirection = false;
	p_damper->f_AlreadyRest = false;
    p_damper->f_RefDoorOpen = false;
    p_damper->f_FrzDoorOpen = false;
}

void Driver_DoubleDamper(void)
{
    DoubleDamperDriver_st *p_damper = &st_DoubleDamperDriver;

    if(p_damper->u8_DoubleDamperPPSTimer > 0)
    {
        p_damper->u8_DoubleDamperPPSTimer--;
    }
    else
    {
        p_damper->u8_DoubleDamperPPSTimer = U8_DOUBLE_DAMPER_RUN_PPS_TIME_HALF_MSEL;

        if(p_damper->u16_DoubleDamperRunSteps > 0)
        {
            p_damper->f_DoubleDamperRunning = true;
            if((false == p_damper->f_RefDoorOpen) && (false == p_damper->f_FrzDoorOpen))
            {
                p_damper->u16_DoubleDamperRunSteps--;
                Set_DoubleDamperStepState(p_damper);
            }
        }
        else
        {
            if(true == p_damper->f_DoubleDamperRunning)
            {
                p_damper->u8_DoubleDamperIdleTimer = U8_DOUBLE_DAMPER_START_DELAY_TIME_SECOND;
                p_damper->f_DoubleDamperRunning = false;
            }
        }
        Set_DoubleDamperIOState(p_damper);
    }
}

static void Set_DoubleDamperStepState(DoubleDamperDriver_st *p_damper)
{
    if(p_damper->em_DoubleDamperStepState >= DOUBLE_DAMPER_STEP_STATE_MAX)
    {
        p_damper->em_DoubleDamperStepState = DOUBLE_DAMPER_STEP_STATE_D;
    }

    if(U8_DOUBLE_DAMPER_OPEN_DIRECTION == (uint8_t)(p_damper->f_DoubleDamperRunDirection))
    {
        // OPEN
        if(DOUBLE_DAMPER_STEP_STATE_D == p_damper->em_DoubleDamperStepState)
        {
            p_damper->em_DoubleDamperStepState = DOUBLE_DAMPER_STEP_STATE_A;
        }
        else
        {
            p_damper->em_DoubleDamperStepState++;
        }
    }
    else
    {
        // CLOSE
        if(DOUBLE_DAMPER_STEP_STATE_A == p_damper->em_DoubleDamperStepState)
        {
            p_damper->em_DoubleDamperStepState = DOUBLE_DAMPER_STEP_STATE_D;
        }
        else
        {
            p_damper->em_DoubleDamperStepState--;
        }
    }
}

static void Set_DoubleDamperIOState(DoubleDamperDriver_st *p_damper)
{
    bool b_IO_En = false;
    bool b_IO_InA = false;
    bool b_IO_InB = false;

    if(false == p_damper->f_DoubleDamperRunning)
    {
        p_damper->em_DoubleDamperStepState = DOUBLE_DAMPER_STEP_STATE_A;
        b_IO_En = false;
        b_IO_InA = false;
        b_IO_InB = false;
    }
    else if((true == p_damper->f_RefDoorOpen) || (true == p_damper->f_FrzDoorOpen))
    {
        b_IO_En = false;
        b_IO_InA = false;
        b_IO_InB = false;
    }
    else
    {
        b_IO_En = true;
        switch(p_damper->em_DoubleDamperStepState)
        {
            case DOUBLE_DAMPER_STEP_STATE_A:
                b_IO_InA = false;
                b_IO_InB = false;
                break;

            case DOUBLE_DAMPER_STEP_STATE_B:
                b_IO_InA = true;
                b_IO_InB = false;
                break;

            case DOUBLE_DAMPER_STEP_STATE_C:
                b_IO_InA = true;
                b_IO_InB = true;
                break;

            case DOUBLE_DAMPER_STEP_STATE_D:
                b_IO_InA = false;
                b_IO_InB = true;
                break;

            default:
                b_IO_InA = false;
                b_IO_InB = false;
                p_damper->em_DoubleDamperStepState = DOUBLE_DAMPER_STEP_STATE_A;
                break;
        }
    }

    Config_DoubleDamperIO(b_IO_En, b_IO_InA, b_IO_InB);
}

static void Config_DoubleDamperIO(bool b_IO_En, bool b_IO_InA, bool b_IO_InB)
{
    if(true == b_IO_En)
    {
        IO_DOUBLE_DAMPER_ENABLE;
    }
    else
    {
        IO_DOUBLE_DAMPER_DISABLE;
    }

    if(true == b_IO_InA)
    {
        IO_DOUBLE_DAMPER_IN1_HIGH;
    }
    else
    {
        IO_DOUBLE_DAMPER_IN1_LOW;
    }

    if(true == b_IO_InB)
    {
        IO_DOUBLE_DAMPER_IN2_HIGH;
    }
    else
    {
        IO_DOUBLE_DAMPER_IN2_LOW;
    }
}

void Set_ActiveDamperState(ActiveDamperState_em em_damper_setting_state)
{
    if(em_damper_setting_state < ACTIVE_DAMPER_STATE_MAX)
    {
        if(false == st_DoubleDamperDriver.f_DoubleDamperNeedToReset)
        {
            st_DoubleDamperDriver.em_ActiveDamperNewState = em_damper_setting_state;
        }
    }
}

void Set_SlaveDamperState(SlaveDamperState_em em_damper_setting_state)
{
    if(em_damper_setting_state < SLAVE_DAMPER_STATE_MAX)
    {
        if(false == st_DoubleDamperDriver.f_DoubleDamperNeedToReset)
        {
            st_DoubleDamperDriver.em_SlaveDamperNewState = em_damper_setting_state;
        }
    }
}

bool IsDoubleDamperAlreadyReset(void)
{
    return st_DoubleDamperDriver.f_AlreadyRest;
}

void Reset_DoubleDamper(void)
{
    st_DoubleDamperDriver.f_DoubleDamperNeedToReset = true;
	if(st_DoubleDamperDriver.f_AlreadyRest == false)
    {
        st_DoubleDamperDriver.f_AlreadyRest = true;
    }
}

void ForcedCtrl_DoubleDamper(bool b_double_damper_forced_state)
{
    DoubleDamperDriver_st *p_damper = &st_DoubleDamperDriver;

    p_damper->f_DoubleDamperNeedToReset = false;
    p_damper->u8_DoubleDamperPPSTimer = U8_DOUBLE_DAMPER_RUN_PPS_TIME_HALF_MSEL;

    p_damper->u16_ActiveDamperNoActionSecond = 0;
    p_damper->u16_SlaveDamperNoActionSecond = 0;

    p_damper->em_DoubleDamperNowState = DOUBLE_DAMPER_STATE_ALLCLOSE;
    p_damper->em_DoubleDamperMidState = DOUBLE_DAMPER_STATE_ALLCLOSE;
    p_damper->em_DoubleDamperNewState = DOUBLE_DAMPER_STATE_ALLCLOSE;

    p_damper->em_ActiveDamperNowState = ACTIVE_DAMPER_STATE_ALLCLOSE;
    p_damper->em_ActiveDamperNewState = ACTIVE_DAMPER_STATE_ALLCLOSE;

    p_damper->em_SlaveDamperNowState = SLAVE_DAMPER_STATE_ALLCLOSE;
    p_damper->em_SlaveDamperNewState = SLAVE_DAMPER_STATE_ALLCLOSE;

    if(true == b_double_damper_forced_state)
    {
        // 强制运行
        p_damper->f_DoubleDamperRunning = true;
        p_damper->f_DoubleDamperRunDirection = false;
        p_damper->u16_DoubleDamperRunSteps = U16_DOUBLE_DAMPER_TEST_STEPS;
    }
    else
    {
        // 强制停止
        p_damper->f_DoubleDamperRunning = false;
        p_damper->u16_DoubleDamperRunSteps = 0;
    }
}

void Handle_DoubleDamperTimer(void)
{
    DoubleDamperDriver_st *p_damper = &st_DoubleDamperDriver;
    uint16_t u16_zone_seneor_value = 0;
    bool b_energy_mode = Get_EnergyConsumptionModeState();
	FridgeState_t fridge_state = Get_FridgeState();

    if(p_damper->u8_DoubleDamperIdleTimer > 0)
    {
        p_damper->u8_DoubleDamperIdleTimer--;
    }

    if(p_damper->u16_ActiveDamperNoActionSecond < 0xFFFF)
    {
        if((false == b_energy_mode) && (eFridge_Showroom != fridge_state))
        {
            p_damper->u16_ActiveDamperNoActionSecond++;
        }

        if(p_damper->u16_ActiveDamperNoActionSecond >= U16_DOUBLE_DAMPER_FORCED_RESET_TIME_SECOND)
        {
            // 强制复位时间到
            p_damper->u16_ActiveDamperNoActionSecond = 0;
            Reset_DoubleDamper();
        }
    }

    if(p_damper->u16_SlaveDamperNoActionSecond < 0xFFFF)
    {
        if((false == b_energy_mode) && (eFridge_Showroom != fridge_state))
        {
            p_damper->u16_SlaveDamperNoActionSecond++;
        }

        if(p_damper->u16_SlaveDamperNoActionSecond >= U16_DOUBLE_DAMPER_FORCED_RESET_TIME_SECOND)
        {
            // 强制复位时间到
            p_damper->u16_SlaveDamperNoActionSecond = 0;
            Reset_DoubleDamper();
        }
    }

    Get_DoubleDamperRunPara();
}

static void Get_DoubleDamperRunPara(void)
{
    DoubleDamperDriver_st *p_damper = &st_DoubleDamperDriver;

    if((0 == p_damper->u8_DoubleDamperIdleTimer) &&
        (false == p_damper->f_DoubleDamperRunning))
    {
        if(p_damper->em_DoubleDamperNewState == p_damper->em_DoubleDamperNowState)
        {
            if(true == p_damper->f_DoubleDamperNeedToReset)
            {
                p_damper->f_DoubleDamperNeedToReset = false;
                p_damper->u16_ActiveDamperNoActionSecond = 0;
                p_damper->u16_SlaveDamperNoActionSecond = 0;
                if(p_damper->em_DoubleDamperNowState < DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT)
                {
                    p_damper->em_DoubleDamperMidState = DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT;
                }
                else
                {
                    p_damper->em_DoubleDamperMidState = DOUBLE_DAMPER_STATE_ALLCLOSE_LIMIT;
                }
            }
            else if((p_damper->em_ActiveDamperNewState != p_damper->em_ActiveDamperNowState) ||
                (p_damper->em_SlaveDamperNewState != p_damper->em_SlaveDamperNowState))
            {
                if(p_damper->em_ActiveDamperNewState != p_damper->em_ActiveDamperNowState)
                {
                    p_damper->em_ActiveDamperNowState = p_damper->em_ActiveDamperNewState;

                    p_damper->u16_ActiveDamperNoActionSecond = 0;
                }

                if(p_damper->em_SlaveDamperNewState != p_damper->em_SlaveDamperNowState)
                {
                    p_damper->em_SlaveDamperNowState = p_damper->em_SlaveDamperNewState;

                    p_damper->u16_SlaveDamperNoActionSecond = 0;
                }

                p_damper->em_DoubleDamperNewState =
                    ary_DoubleDamperSate[(uint8_t)p_damper->em_SlaveDamperNowState][(uint8_t)p_damper->em_ActiveDamperNowState];
            }
            else if(p_damper->em_DoubleDamperNewState !=
                ary_DoubleDamperSate[(uint8_t)p_damper->em_SlaveDamperNowState][(uint8_t)p_damper->em_ActiveDamperNowState])
            {
                p_damper->em_DoubleDamperNewState =
                    ary_DoubleDamperSate[(uint8_t)p_damper->em_SlaveDamperNowState][(uint8_t)p_damper->em_ActiveDamperNowState];
            }
        }
        else if(p_damper->em_DoubleDamperNewState < p_damper->em_DoubleDamperNowState)
        {
            if(p_damper->em_DoubleDamperNowState < DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT)
            {
                p_damper->em_DoubleDamperMidState = DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT;
            }
            else
            {
                p_damper->em_DoubleDamperMidState = DOUBLE_DAMPER_STATE_ALLCLOSE_LIMIT;
            }
        }
        else if(p_damper->em_DoubleDamperNewState <= DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT)
        {
            p_damper->em_DoubleDamperMidState = p_damper->em_DoubleDamperNewState;
        }
        else if(p_damper->em_DoubleDamperNowState < DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT)
        {
            p_damper->em_DoubleDamperMidState = DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT;
        }
        else
        {
            p_damper->em_DoubleDamperMidState = p_damper->em_DoubleDamperNewState;
        }

        if(p_damper->em_DoubleDamperMidState != p_damper->em_DoubleDamperNowState)
        {
            if(p_damper->em_DoubleDamperNowState < DOUBLE_DAMPER_STATE_ALLOPEN_LIMIT)
            {
                p_damper->f_DoubleDamperRunDirection = true;
            }
            else
            {
                p_damper->f_DoubleDamperRunDirection = false;
            }

            p_damper->u8_DoubleDamperPPSTimer = U8_DOUBLE_DAMPER_RUN_PPS_TIME_HALF_MSEL;
            p_damper->u16_DoubleDamperRunSteps =
                ary_DoubleDamperRunData[(uint8_t)p_damper->em_DoubleDamperNowState][(uint8_t)p_damper->em_DoubleDamperMidState];
            p_damper->em_DoubleDamperNowState = p_damper->em_DoubleDamperMidState;

            p_damper->f_DoubleDamperRunning = true;
        }
    }
}

bool Get_ActiveDamperState(void)
{
    bool b_damper_state = true;

    if(ACTIVE_DAMPER_STATE_ALLCLOSE == st_DoubleDamperDriver.em_ActiveDamperNowState)
    {
        b_damper_state = false;
    }

    return b_damper_state;
}

bool Get_SlaveDamperState(void)
{
    bool b_damper_state = true;

    if(SLAVE_DAMPER_STATE_ALLCLOSE == st_DoubleDamperDriver.em_SlaveDamperNowState)
    {
        b_damper_state = false;
    }

    return b_damper_state;
}

void Set_RefDoorState_DoubleDamper(bool b_door_state)
{
    if(true == b_door_state)
    {
        st_DoubleDamperDriver.f_RefDoorOpen = true;
    }
    else
    {
        st_DoubleDamperDriver.f_RefDoorOpen = false;
    }
}

void Set_FrzDoorState_DoubleDamper(bool b_door_state)
{
    if(true == b_door_state)
    {
        st_DoubleDamperDriver.f_FrzDoorOpen = true;
    }
    else
    {
        st_DoubleDamperDriver.f_FrzDoorOpen = false;
    }
}
