{"files.associations": {"reset.h": "c", "verticalbeamheater.h": "c", "driver_adsample.h": "c", "frzfanresolver.h": "c", "core_types.h": "c", "resolverdevicepriority.h": "c", "stdbool.h": "c", "driver_graduallamp.h": "c", "assert.h": "c", "core_assert.h": "c", "driver_doorswitch.h": "c", "io_device.h": "c", "adpt_gpio.h": "c", "gpio.h": "c", "winapifamily.h": "c", "base_types.h": "c", "parameter_temperaturezone.h": "c", "driver_adtemperature.h": "c", "adpt_adc.h": "c", "adpt_pwm.h": "c", "core_callbacktimer.h": "c", "fridgerunner.h": "c", "ledcontroller.h": "c", "syslog.h": "c", "sysctrl.h": "c", "stddef.h": "c", "coolingcycle.h": "c", "resolverdevice.h": "c", "systemtimermodule.h": "c", "miio_api.h": "c", "factorymode.h": "c", "faultcode.h": "c", "stdint.h": "c", "stdint-gcc.h": "c", "driver_doubledamper.h": "c", "driver_fan.h": "c", "array": "c", "string": "c", "string_view": "c", "atomic": "c", "bit": "c", "charconv": "c", "compare": "c", "cstddef": "c", "format": "c", "iterator": "c", "limits": "c", "memory": "c", "sstream": "c", "system_error": "c", "type_traits": "c", "xiosbase": "c", "xlocale": "c", "xlocbuf": "c", "xlocmon": "c", "xloctime": "c", "xmemory": "c", "xstring": "c", "xutility": "c", "displayinterface.h": "c", "core_timerlibrary.h": "c", "iosfwd": "c", "simplefsm.h": "c", "systemmanager.h": "c", "testusart.h": "c", "testmode.h": "c", "showroommode.h": "c", "driver_flash.h": "c", "functionalcircuittest.h": "c", "utility": "c", "tuple": "c", "xtr1common": "c", "driver_compfrequency.h": "c"}}